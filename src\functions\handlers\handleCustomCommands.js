const fs = require('fs');
const path = require('path');
const { SlashCommandBuilder, REST, Routes } = require('discord.js');

const CUSTOM_COMMANDS_FILE = path.join(__dirname, '../../data/customCommands.json');

module.exports = (client) => {
    client.handleCustomCommands = {
        // Load custom commands from JSON file
        loadCustomCommands() {
            try {
                if (!fs.existsSync(CUSTOM_COMMANDS_FILE)) {
                    return {};
                }
                const data = fs.readFileSync(CUSTOM_COMMANDS_FILE, 'utf8');
                return JSON.parse(data);
            } catch (error) {
                console.error('Error loading custom commands:', error);
                return {};
            }
        },

        // Save custom commands to JSON file
        saveCustomCommands(commands) {
            try {
                // Ensure the data directory exists
                const dataDir = path.dirname(CUSTOM_COMMANDS_FILE);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }
                
                fs.writeFileSync(CUSTOM_COMMANDS_FILE, JSON.stringify(commands, null, 2));
                return true;
            } catch (error) {
                console.error('Error saving custom commands:', error);
                return false;
            }
        },

        // Register custom commands with Discord
        async registerCustomCommands() {
            const customCommands = this.loadCustomCommands();
            const customCommandArray = [];

            // Convert custom commands to Discord command format
            for (const [name, command] of Object.entries(customCommands)) {
                const slashCommand = new SlashCommandBuilder()
                    .setName(name)
                    .setDescription(command.description);

                customCommandArray.push(slashCommand.toJSON());
            }

            // Add custom commands to the client's command array
            if (client.commandArray) {
                // Remove old custom commands from the array
                client.commandArray = client.commandArray.filter(cmd => 
                    !this.isCustomCommand(cmd.name)
                );
                
                // Add new custom commands
                client.commandArray.push(...customCommandArray);
            }

            // Register with Discord if we have the necessary environment variables
            const clientId = process.env.DISCORD_CLIENT_ID;
            const guildIds = process.env.DISCORD_GUILD_IDS ? process.env.DISCORD_GUILD_IDS.split(',') : [];

            if (clientId && process.env.TOKEN) {
                const rest = new REST({ version: "10" }).setToken(process.env.TOKEN);

                try {
                    if (guildIds.length > 0) {
                        for (const guildId of guildIds) {
                            await rest.put(Routes.applicationGuildCommands(clientId, guildId), {
                                body: client.commandArray,
                            });
                        }
                    } else {
                        await rest.put(Routes.applicationCommands(clientId), {
                            body: client.commandArray,
                        });
                    }
                    console.log('✅ Successfully updated custom commands with Discord.');
                } catch (error) {
                    console.error('❌ Error updating custom commands with Discord:', error);
                }
            }
        },

        // Check if a command is a custom command
        isCustomCommand(commandName) {
            const customCommands = this.loadCustomCommands();
            return customCommands.hasOwnProperty(commandName);
        },

        // Execute a custom command
        async executeCustomCommand(interaction) {
            const customCommands = this.loadCustomCommands();
            const command = customCommands[interaction.commandName];

            if (!command) {
                return interaction.reply({
                    content: 'This custom command no longer exists.',
                    ephemeral: true
                });
            }

            try {
                // Parse the response for any special formatting
                let response = command.response;

                // Replace placeholders with actual values
                response = response.replace(/\{user\}/g, `<@${interaction.user.id}>`);
                response = response.replace(/\{username\}/g, interaction.user.username);
                response = response.replace(/\{server\}/g, interaction.guild.name);
                response = response.replace(/\{channel\}/g, `<#${interaction.channel.id}>`);

                // Update usage count
                command.usageCount = (command.usageCount || 0) + 1;
                command.lastUsed = Date.now();
                customCommands[interaction.commandName] = command;
                this.saveCustomCommands(customCommands);

                await interaction.reply({ content: response });
            } catch (error) {
                console.error('Error executing custom command:', error);
                await interaction.reply({
                    content: 'There was an error executing this custom command.',
                    ephemeral: true
                });
            }
        },

        // Create a new custom command
        async createCustomCommand(name, description, response, createdBy) {
            const customCommands = this.loadCustomCommands();

            // Check if command already exists
            if (customCommands[name] || client.commands.has(name)) {
                return { success: false, message: 'A command with this name already exists.' };
            }

            // Validate command name
            if (!/^[a-z0-9_-]{1,32}$/.test(name)) {
                return { 
                    success: false, 
                    message: 'Command name must be 1-32 characters long and contain only lowercase letters, numbers, hyphens, and underscores.' 
                };
            }

            // Create the command
            const newCommand = {
                description,
                response,
                createdBy,
                createdAt: Date.now(),
                usageCount: 0,
                lastUsed: null
            };

            customCommands[name] = newCommand;

            if (this.saveCustomCommands(customCommands)) {
                // Re-register commands with Discord
                await this.registerCustomCommands();
                return { success: true, message: 'Custom command created successfully!' };
            } else {
                return { success: false, message: 'Failed to save the custom command.' };
            }
        },

        // Update an existing custom command
        async updateCustomCommand(name, description, response, updatedBy) {
            const customCommands = this.loadCustomCommands();

            if (!customCommands[name]) {
                return { success: false, message: 'Command not found.' };
            }

            // Update the command
            customCommands[name].description = description;
            customCommands[name].response = response;
            customCommands[name].updatedBy = updatedBy;
            customCommands[name].updatedAt = Date.now();

            if (this.saveCustomCommands(customCommands)) {
                // Re-register commands with Discord
                await this.registerCustomCommands();
                return { success: true, message: 'Custom command updated successfully!' };
            } else {
                return { success: false, message: 'Failed to update the custom command.' };
            }
        },

        // Delete a custom command
        async deleteCustomCommand(name) {
            const customCommands = this.loadCustomCommands();

            if (!customCommands[name]) {
                return { success: false, message: 'Command not found.' };
            }

            delete customCommands[name];

            if (this.saveCustomCommands(customCommands)) {
                // Re-register commands with Discord
                await this.registerCustomCommands();
                return { success: true, message: 'Custom command deleted successfully!' };
            } else {
                return { success: false, message: 'Failed to delete the custom command.' };
            }
        }
    };

    // Initialize custom commands on startup
    client.handleCustomCommands.registerCustomCommands();
};
