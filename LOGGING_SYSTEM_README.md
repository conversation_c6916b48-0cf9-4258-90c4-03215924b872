# Discord Bot Logging System

A comprehensive logging system that tracks server events, moderation actions, and provides detailed audit trails for Discord servers.

## Features

### 📋 **Event Logging**
- **Message Events**: Deletions and edits
- **Member Events**: Joins and leaves
- **Moderation Events**: Bans, unbans, kicks, timeouts
- **Role Events**: Role additions and removals
- **Voice Events**: Voice channel activity
- **Channel Events**: Channel modifications

### 🔍 **Audit System**
- **User History**: Complete audit trail for any user
- **Filtering**: Filter by event type and time range
- **Export**: Export audit data for external analysis
- **Pagination**: Easy navigation through large datasets

### ⚙️ **Configuration**
- **Per-Channel Setup**: Different channels for different event types
- **Selective Logging**: Enable/disable specific event types
- **Easy Management**: Interactive setup with dropdown menus

## Commands

### `/logs setup`
Configure logging channels for different event types through an interactive interface.

**Features:**
- Category-based configuration (Message, Member, Moderation, etc.)
- Channel selection for each event type
- View current settings
- Easy-to-use dropdown menus

**Usage:**
```
/logs setup
```

### `/logs view <type> [limit]`
View recent logs of a specific type.

**Parameters:**
- `type`: The type of logs to view (required)
  - Message Deletions
  - Message Edits
  - Member Joins/Leaves
  - Bans/Unbans/Kicks/Timeouts
  - Role Changes
  - Channel Changes
  - Voice Activity
  - All Events
- `limit`: Number of logs to display (1-50, default: 10)

**Usage:**
```
/logs view type:Message Deletions limit:20
/logs view type:All Events
```

### `/logs disable <type>`
Disable logging for specific event types.

**Parameters:**
- `type`: The type of logging to disable
  - Individual event types
  - All Logging (disables everything)

**Usage:**
```
/logs disable type:Message Deletions
/logs disable type:All Logging
```

### `/audit <user> [type] [days]`
View detailed audit history for a specific user.

**Parameters:**
- `user`: The user to view audit history for (required)
- `type`: Filter by event type (optional)
  - All Events
  - Moderation Actions
  - Message Events
  - Voice Events
  - Role Changes
  - Join/Leave Events
- `days`: Number of days to look back (1-90, default: 30)

**Features:**
- **Pagination**: Navigate through multiple pages of results
- **Summary**: Overview of user's activity
- **Export**: Export detailed audit data
- **Time Filtering**: Customizable time ranges

**Usage:**
```
/audit user:@username
/audit user:@username type:Moderation Actions days:7
/audit user:@username type:All Events days:90
```

## Event Types

### 📝 **Message Events**
- **Message Deletions**: Logs when messages are deleted
  - Author information
  - Channel and message ID
  - Original message content
  - Timestamp

- **Message Edits**: Logs when messages are edited
  - Author information
  - Before and after content
  - Channel and message ID
  - Timestamp

### 👥 **Member Events**
- **Member Joins**: Logs when users join the server
  - User information and avatar
  - Account creation date
  - Current member count
  - Join timestamp

- **Member Leaves**: Logs when users leave the server
  - User information
  - Time spent in server
  - Current member count
  - Leave timestamp

### 🔨 **Moderation Events**
- **Bans**: Logs when members are banned
  - Target and moderator information
  - Reason for ban
  - Timestamp

- **Unbans**: Logs when members are unbanned
- **Kicks**: Logs when members are kicked
- **Timeouts**: Logs when members are timed out
  - Duration of timeout
  - Reason for timeout

### 🎭 **Role Events**
- **Role Changes**: Logs when member roles are modified
  - User information
  - Roles added and removed
  - Timestamp

### 🔊 **Voice Events**
- **Voice Activity**: Logs voice channel activity
  - Join/leave events
  - Channel switching
  - User information

## Data Storage

### **Log Settings** (`src/data/logSettings.json`)
Stores configuration for which channels should receive which types of logs.

```json
{
  "guildId": {
    "message_delete": "channelId",
    "message_edit": "channelId",
    "member_join": "channelId",
    "moderation": "channelId"
  }
}
```

### **Audit Logs** (`src/data/auditLogs.json`)
Stores detailed event history for audit purposes.

```json
{
  "guildId": {
    "message_delete": [
      {
        "userId": "userId",
        "action": "Message Deleted",
        "description": "Message deleted in #channel",
        "timestamp": 1234567890000,
        "messageContent": "Original message",
        "channelId": "channelId",
        "messageId": "messageId"
      }
    ]
  }
}
```

## File Structure

```
src/
├── commands/admin/
│   ├── logs.js              # Main logging command
│   └── audit.js             # User audit command
├── functions/handlers/
│   └── handleLogging.js     # Logging system handler
├── data/
│   ├── logSettings.json     # Logging configuration
│   └── auditLogs.json       # Audit history storage
└── events/client/
    └── interactionCreate.js # Updated with logging handlers
```

## Setup Instructions

### 1. **Initial Setup**
The logging system is automatically loaded when the bot starts. No additional configuration required.

### 2. **Configure Logging**
Use `/logs setup` to configure which channels should receive different types of logs:

1. Run `/logs setup`
2. Select a category (Message Events, Member Events, etc.)
3. Choose specific event types to configure
4. Select the channel where logs should be sent
5. Repeat for other event types as needed

### 3. **Permissions**
- **Setup/Configuration**: Requires Administrator permission
- **Viewing Logs**: Requires Administrator permission
- **Audit History**: Requires Moderate Members permission

## Integration with Existing Commands

The logging system automatically integrates with existing moderation commands:

- **Ban Command**: Logs ban actions with moderator and reason
- **Kick Command**: Logs kick actions with moderator and reason
- **Timeout Commands**: Logs timeout actions with duration and reason

## Event Listeners

The system automatically listens for Discord events:

- `messageDelete` - Logs message deletions
- `messageUpdate` - Logs message edits
- `guildMemberAdd` - Logs member joins
- `guildMemberRemove` - Logs member leaves
- `guildMemberUpdate` - Logs role changes

## Data Management

### **Automatic Cleanup**
- Keeps only the last 1000 entries per event type per guild
- Prevents log files from growing too large
- Maintains performance while preserving recent history

### **Export Functionality**
- Export audit data in JSON format
- Includes user information, event details, and timestamps
- Useful for external analysis or record keeping

## Troubleshooting

### **Logs Not Appearing**
1. Check if logging is configured for the event type (`/logs setup`)
2. Verify the bot has permission to send messages in the log channel
3. Ensure the log channel still exists

### **Missing Events**
1. Verify the bot has the necessary permissions to see events
2. Check if the event type is enabled in the configuration
3. Ensure the bot is online when events occur

### **Performance Issues**
1. The system automatically limits stored entries to prevent performance issues
2. Consider using more specific event types instead of logging everything
3. Regularly review and clean up old log channels

## Advanced Features

### **Filtering and Search**
- Filter audit logs by event type
- Search within specific time ranges
- Export filtered results

### **Moderation Integration**
- Automatic logging of all moderation actions
- Tracks moderator, target, reason, and timestamp
- Provides complete audit trail for moderation decisions

### **Customization**
- Easy to extend with new event types
- Configurable retention periods
- Flexible channel assignment

## Future Enhancements

Potential improvements that could be added:
- Database integration for better performance
- Advanced search and filtering options
- Automated report generation
- Integration with external logging services
- Real-time log streaming
- Advanced analytics and insights
