const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

const AUDIT_LOGS_FILE = path.join(__dirname, '../../data/auditLogs.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('audit')
        .setDescription('View detailed audit history for a user')
        .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers)
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to view audit history for')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Filter by specific event type')
                .setRequired(false)
                .addChoices(
                    { name: 'All Events', value: 'all' },
                    { name: 'Moderation Actions', value: 'moderation' },
                    { name: 'Message Events', value: 'messages' },
                    { name: 'Voice Events', value: 'voice' },
                    { name: 'Role Changes', value: 'roles' },
                    { name: 'Join/Leave Events', value: 'membership' }
                ))
        .addIntegerOption(option =>
            option.setName('days')
                .setDescription('Number of days to look back (1-90)')
                .setMinValue(1)
                .setMaxValue(90)),

    async execute(interaction) {
        const user = interaction.options.getUser('user');
        const filterType = interaction.options.getString('type') || 'all';
        const daysBack = interaction.options.getInteger('days') || 30;
        const guildId = interaction.guild.id;

        await interaction.deferReply({ ephemeral: true });

        const auditLogs = loadAuditLogs();
        const guildLogs = auditLogs[guildId] || {};
        
        // Collect all logs for the user
        let userLogs = [];
        const cutoffTime = Date.now() - (daysBack * 24 * 60 * 60 * 1000);

        for (const [logType, logs] of Object.entries(guildLogs)) {
            if (Array.isArray(logs)) {
                const userSpecificLogs = logs.filter(log => 
                    (log.userId === user.id || log.targetId === user.id) && 
                    log.timestamp > cutoffTime
                );
                
                userLogs.push(...userSpecificLogs.map(log => ({ ...log, logType })));
            }
        }

        // Filter by type if specified
        if (filterType !== 'all') {
            userLogs = userLogs.filter(log => {
                switch (filterType) {
                    case 'moderation':
                        return ['ban', 'unban', 'kick', 'timeout', 'untimeout'].includes(log.logType);
                    case 'messages':
                        return ['message_delete', 'message_edit'].includes(log.logType);
                    case 'voice':
                        return log.logType === 'voice_activity';
                    case 'roles':
                        return log.logType === 'role_change';
                    case 'membership':
                        return ['member_join', 'member_leave'].includes(log.logType);
                    default:
                        return true;
                }
            });
        }

        // Sort by timestamp (newest first)
        userLogs.sort((a, b) => b.timestamp - a.timestamp);

        if (userLogs.length === 0) {
            const embed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('📋 No Audit History Found')
                .setDescription(`No audit history found for ${user.tag} in the last ${daysBack} days.`)
                .addFields({
                    name: 'Search Parameters',
                    value: `**User:** ${user.tag}\n**Filter:** ${getFilterDisplayName(filterType)}\n**Time Range:** Last ${daysBack} days`,
                    inline: false
                })
                .setTimestamp();

            return interaction.editReply({ embeds: [embed] });
        }

        // Pagination setup
        const itemsPerPage = 5;
        const totalPages = Math.ceil(userLogs.length / itemsPerPage);
        let currentPage = 0;

        const generateEmbed = (page) => {
            const start = page * itemsPerPage;
            const end = start + itemsPerPage;
            const pageLogs = userLogs.slice(start, end);

            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle(`📋 Audit History: ${user.tag}`)
                .setDescription(`Showing ${pageLogs.length} of ${userLogs.length} events from the last ${daysBack} days`)
                .setThumbnail(user.displayAvatarURL({ dynamic: true }))
                .setFooter({ text: `Page ${page + 1} of ${totalPages} • Filter: ${getFilterDisplayName(filterType)}` })
                .setTimestamp();

            // Add summary field
            const summary = generateSummary(userLogs);
            embed.addFields({
                name: '📊 Summary',
                value: summary,
                inline: false
            });

            // Add individual log entries
            pageLogs.forEach((log, index) => {
                const timestamp = `<t:${Math.floor(log.timestamp / 1000)}:R>`;
                const logTypeEmoji = getLogTypeEmoji(log.logType);
                
                let fieldValue = `${log.description}\n**Time:** ${timestamp}`;
                
                if (log.moderator && log.moderator !== log.userId) {
                    fieldValue += `\n**Moderator:** <@${log.moderator}>`;
                }
                
                if (log.reason) {
                    fieldValue += `\n**Reason:** ${log.reason}`;
                }
                
                if (log.duration) {
                    fieldValue += `\n**Duration:** ${log.duration}`;
                }

                embed.addFields({
                    name: `${logTypeEmoji} ${log.action || getLogTypeDisplayName(log.logType)}`,
                    value: fieldValue,
                    inline: false
                });
            });

            return embed;
        };

        const generateButtons = (page) => {
            const row = new ActionRowBuilder();

            if (totalPages > 1) {
                row.addComponents(
                    new ButtonBuilder()
                        .setCustomId('audit_prev_page')
                        .setLabel('◀️ Previous')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(page === 0),
                    new ButtonBuilder()
                        .setCustomId('audit_next_page')
                        .setLabel('Next ▶️')
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(page === totalPages - 1)
                );
            }

            // Add export button
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId(`audit_export_${user.id}`)
                    .setLabel('📄 Export')
                    .setStyle(ButtonStyle.Primary)
            );

            return row.components.length > 0 ? [row] : [];
        };

        const embed = generateEmbed(currentPage);
        const components = generateButtons(currentPage);

        const response = await interaction.editReply({
            embeds: [embed],
            components: components
        });

        if (totalPages > 1) {
            const collector = response.createMessageComponentCollector({
                time: 300000 // 5 minutes
            });

            collector.on('collect', async (i) => {
                if (i.user.id !== interaction.user.id) {
                    return i.reply({ content: 'You cannot interact with this audit report.', ephemeral: true });
                }

                if (i.customId === 'audit_prev_page') {
                    currentPage = Math.max(0, currentPage - 1);
                    const newEmbed = generateEmbed(currentPage);
                    const newComponents = generateButtons(currentPage);
                    await i.update({ embeds: [newEmbed], components: newComponents });
                } else if (i.customId === 'audit_next_page') {
                    currentPage = Math.min(totalPages - 1, currentPage + 1);
                    const newEmbed = generateEmbed(currentPage);
                    const newComponents = generateButtons(currentPage);
                    await i.update({ embeds: [newEmbed], components: newComponents });
                } else if (i.customId.startsWith('audit_export_')) {
                    await handleExportAudit(i, user, userLogs, daysBack, filterType);
                }
            });

            collector.on('end', async () => {
                try {
                    await response.edit({ components: [] });
                } catch (error) {
                    // Message might have been deleted
                }
            });
        }
    }
};

// Load audit logs from JSON file
function loadAuditLogs() {
    try {
        if (!fs.existsSync(AUDIT_LOGS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(AUDIT_LOGS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading audit logs:', error);
        return {};
    }
}

// Generate summary of user's audit history
function generateSummary(logs) {
    const summary = {};
    
    logs.forEach(log => {
        const type = log.logType;
        summary[type] = (summary[type] || 0) + 1;
    });

    const summaryLines = Object.entries(summary)
        .sort(([,a], [,b]) => b - a)
        .map(([type, count]) => `${getLogTypeEmoji(type)} ${getLogTypeDisplayName(type)}: ${count}`)
        .slice(0, 6); // Show top 6 event types

    return summaryLines.join('\n') || 'No events found';
}

// Handle export audit functionality
async function handleExportAudit(interaction, user, logs, daysBack, filterType) {
    const exportData = {
        user: {
            id: user.id,
            tag: user.tag,
            username: user.username
        },
        exportDate: new Date().toISOString(),
        timeRange: `${daysBack} days`,
        filter: filterType,
        totalEvents: logs.length,
        events: logs.map(log => ({
            type: log.logType,
            action: log.action,
            description: log.description,
            timestamp: new Date(log.timestamp).toISOString(),
            moderator: log.moderator,
            reason: log.reason,
            duration: log.duration
        }))
    };

    const exportText = JSON.stringify(exportData, null, 2);
    
    // Create a simple text summary for Discord
    const textSummary = `**Audit Report for ${user.tag}**\n` +
        `**Export Date:** ${new Date().toLocaleString()}\n` +
        `**Time Range:** Last ${daysBack} days\n` +
        `**Filter:** ${getFilterDisplayName(filterType)}\n` +
        `**Total Events:** ${logs.length}\n\n` +
        `**Recent Events:**\n` +
        logs.slice(0, 10).map(log => 
            `• ${getLogTypeEmoji(log.logType)} ${log.action || log.logType} - <t:${Math.floor(log.timestamp / 1000)}:R>`
        ).join('\n') +
        (logs.length > 10 ? `\n... and ${logs.length - 10} more events` : '');

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle('📄 Audit Export')
        .setDescription(textSummary)
        .setFooter({ text: 'Full JSON export available on request' })
        .setTimestamp();

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

// Helper functions
function getFilterDisplayName(filter) {
    const displayNames = {
        'all': 'All Events',
        'moderation': 'Moderation Actions',
        'messages': 'Message Events',
        'voice': 'Voice Events',
        'roles': 'Role Changes',
        'membership': 'Join/Leave Events'
    };
    return displayNames[filter] || filter;
}

function getLogTypeDisplayName(type) {
    const displayNames = {
        'message_delete': 'Message Deleted',
        'message_edit': 'Message Edited',
        'member_join': 'Member Joined',
        'member_leave': 'Member Left',
        'ban': 'Banned',
        'unban': 'Unbanned',
        'kick': 'Kicked',
        'timeout': 'Timed Out',
        'untimeout': 'Timeout Removed',
        'role_change': 'Role Changed',
        'channel_change': 'Channel Changed',
        'voice_activity': 'Voice Activity'
    };
    return displayNames[type] || type;
}

function getLogTypeEmoji(type) {
    const emojis = {
        'message_delete': '🗑️',
        'message_edit': '✏️',
        'member_join': '📥',
        'member_leave': '📤',
        'ban': '🔨',
        'unban': '🔓',
        'kick': '👢',
        'timeout': '⏰',
        'untimeout': '⏰',
        'role_change': '🎭',
        'channel_change': '📝',
        'voice_activity': '🔊'
    };
    return emojis[type] || '📋';
}
