const { Events, InteractionType, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

// Helper function to create the embed for a specific page
function createPageEmbed(page) {
    let embed;

    switch (page) {
        case 2:
            // Section 2: الرومات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الرومات')
                .setDescription(`
                    ⚠️ **تنبيه هام لكل الأعضاء الجدد!**

في مجتمع سهم، لكل روم قوانينه ونظامه الخاص. لا يوجد روم يشبه الآخر، فكل قناة تقدم محتوى مختلف، أنشطة مميزة، وأسلوب تفاعل خاص بها.

لذلك نرجو منك قراءة أوصاف القنوات بعناية لتفهم طبيعة كل روم قبل المشاركة فيه.

وإذا أردت استعراض أوصاف القنوات، يُرجى اختيار الفئات من القائمة الموجودة بالأسفل، وسيتم عرض كل المعلومات التي تحتاجها بكل وضوح.
                `)
                .setFooter({ text: 'صفحة 2 من 4' })
                .setTimestamp();
            break;

        case 3:
            // Section 3: الرتب و الرولات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الرتب و الرولات')
                .setDescription(`
**🎭 وصف الرُتب (Roles Description)**

لكل رول في سيرفر سهم فكرته الخاصة، وميزاته، وحتى قواعده التي تميّزه عن غيره.
نعلم أن عدد الرُتب قد يكون مُربكًا في البداية، وقد تجد صعوبة في معرفة الفرق بينها أو وظيفتها.

لهذا السبب، قمنا بتقسيم الرولات إلى فئات واضحة لتسهيل فهمها.

🎯 اختر إحدى الفئات من القائمة أدناه، وسنرسل لك وصفًا شاملاً لتلك الفئة على شكل حزمة منظمة.
📦 بمجرد اختيارك، ستصلك التفاصيل مباشرة.                `)
                .setFooter({ text: 'صفحة 3 من 4' })
                .setTimestamp();
            break;

        case 4:
            // Section 4: الأسئلة الشائعة
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الأسئلة الشائعة')
                .setDescription(`
👋 **للعضو الجديد – دليل المساعدة الأولية**

بصفتك عضوًا جديدًا في سيرفر سهم، قد تُواجه بعض الأمور التي يصعب فهمها في البداية، سواء في طريقة استخدام السيرفر أو في التعامل مع مميزاته وأنظمته المختلفة.


📚 لقد أنشأنا قناة خاصة تحتوي على الأسئلة الشائعة وشرح لهياكل السيرفر، لتكون بداية سهلة وواضحة لك.

🧭 كل ما عليك هو اختيار أحد الخيارات من القائمة أدناه، وسنقوم بإرسال التفسيرات والمعلومات المرتبطة مباشرة لك.                `)
                .setFooter({ text: 'صفحة 4 من 4' })
                .setTimestamp();
            break;

        default:
            // Section 1: المعلومات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('المعلومات')
                .setDescription(`
                    **مرحبًا بك في مجتمع سهم!**

كعضو جديد، قد تواجه بعض الأمور التي تبدو مُربِكة في البداية، سواء في استخدام تطبيق الديسكورد نفسه أو في .التفاعل داخل السيرفر

لذلك، وبعد جهد واهتمام كبير، قمنا بإعداد دليل يحتوي على أكثر الأسئلة الشائعة التي قد تدور في بالك. هذا الدليل سيساعدك على فهم كل ما تحتاج معرفته لتبدأ تجربتك معنا بكل سهولة ووضوح.
                `)
                .setFooter({ text: 'صفحة 1 من 4' })
                .setTimestamp();
            break;
    }

    return embed;
}

// Helper function to create the components for a specific page
function createPageComponents(page) {
    let components = [];

    // Create navigation buttons
    const navigationRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId(`server_map_prev_${page}`)
            .setLabel('◀️')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(page <= 1),
        new ButtonBuilder()
            .setCustomId(`server_map_page_${page}`)
            .setLabel(`${page}/4`)
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(true),
        new ButtonBuilder()
            .setCustomId(`server_map_next_${page}`)
            .setLabel('▶️')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(page >= 4)
    );

    // Add back button
    const backButton = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('back_to_main')
            .setLabel('رجوع')
            .setStyle(ButtonStyle.Danger)
    );

    // Add page-specific components
    switch (page) {
        case 2:
            // Add dropdown for channel categories
            const channelDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_channels')
                        .setPlaceholder('اختر فئة القنوات')
                        .addOptions([
                            {
                                label: 'Pinboard',
                                description: 'معلومات عن قنوات Pinboard',
                                value: 'pinboard_channels',
                                emoji: '📌'
                            },
                            {
                                label: 'Community',
                                description: 'معلومات عن قنوات المجتمع',
                                value: 'community_channels',
                                emoji: '✨'
                            },
                            {
                                label: 'Hangout',
                                description: 'معلومات عن قنوات الدردشة',
                                value: 'hangout_channels',
                                emoji: '🔊'
                            }
                        ])
                );
            components = [channelDropdown, navigationRow, backButton];
            break;

        case 3:
            // Add dropdown for role categories
            const rolesDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_roles')
                        .setPlaceholder('اختر فئة الرتب')
                        .addOptions([
                            {
                                label: 'Staff',
                                description: 'معلومات عن رتب الإدارة',
                                value: 'staff_roles',
                                emoji: '🛠️'
                            },
                            {
                                label: 'Notifications',
                                description: 'معلومات عن رتب الإشعارات',
                                value: 'notification_roles',
                                emoji: '🔔'
                            },
                            {
                                label: 'Community',
                                description: 'معلومات عن رتب المجتمع',
                                value: 'community_roles',
                                emoji: '👥'
                            },
                            {
                                label: 'Creators',
                                description: 'معلومات عن رتب المنشئين',
                                value: 'creator_roles',
                                emoji: '🎨'
                            },
                            {
                                label: 'Colors',
                                description: 'معلومات عن رتب الألوان',
                                value: 'color_roles',
                                emoji: '🎨'
                            }
                        ])
                );
            components = [rolesDropdown, navigationRow, backButton];
            break;

        case 4:
            // Add dropdown for FAQ categories
            const faqDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_faq')
                        .setPlaceholder('اختر فئة الأسئلة')
                        .addOptions([
                            {
                                label: 'Frequently Asked Questions',
                                description: 'الأسئلة المتداولة',
                                value: 'faq_general',
                                emoji: '❓'
                            }
                        ])
                );
            components = [faqDropdown, navigationRow, backButton];
            break;

        default:
            components = [navigationRow, backButton];
            break;
    }

    return components;
}

// Function to show a specific page of the server map
async function showServerMapPage(interaction, page, isEdit = false) {
    const embed = createPageEmbed(page);
    const components = createPageComponents(page);

    // Send or edit the message
    if (isEdit) {
        await interaction.editReply({
            embeds: [embed],
            components: components
        });
    } else {
        await interaction.reply({
            embeds: [embed],
            components: components,
            ephemeral: true
        });
    }
}

// Store embeds being created or edited
const embedBuilderSessions = new Map();

// Handle embed builder button interactions
async function handleEmbedBuilderInteraction(interaction, client) {
    const customId = interaction.customId;
    const message = interaction.message;
    const currentEmbed = message.embeds[0];

    // Get the current embed from the message
    const embedBuilder = EmbedBuilder.from(currentEmbed);

    switch (customId) {
        case 'embed_title':
            // Create a modal for title input
            const titleModal = new ModalBuilder()
                .setCustomId('embed_modal_title')
                .setTitle('Set Embed Title');

            const titleInput = new TextInputBuilder()
                .setCustomId('titleInput')
                .setLabel('Title')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the title for your embed')
                .setValue(currentEmbed.title || '')
                .setRequired(false);

            const titleActionRow = new ActionRowBuilder().addComponents(titleInput);
            titleModal.addComponents(titleActionRow);

            await interaction.showModal(titleModal);
            break;

        case 'embed_description':
            // Create a modal for description input
            const descModal = new ModalBuilder()
                .setCustomId('embed_modal_description')
                .setTitle('Set Embed Description');

            const descInput = new TextInputBuilder()
                .setCustomId('descriptionInput')
                .setLabel('Description')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Enter the description for your embed')
                .setValue(currentEmbed.description || '')
                .setRequired(false);

            const descActionRow = new ActionRowBuilder().addComponents(descInput);
            descModal.addComponents(descActionRow);

            await interaction.showModal(descModal);
            break;

        case 'embed_color':
            // Create a modal for color input
            const colorModal = new ModalBuilder()
                .setCustomId('embed_modal_color')
                .setTitle('Set Embed Color');

            const colorInput = new TextInputBuilder()
                .setCustomId('colorInput')
                .setLabel('Color (HEX code)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('#0099ff')
                .setValue(currentEmbed.color ? `#${currentEmbed.color.toString(16).padStart(6, '0')}` : '#0099ff')
                .setRequired(false);

            const colorActionRow = new ActionRowBuilder().addComponents(colorInput);
            colorModal.addComponents(colorActionRow);

            await interaction.showModal(colorModal);
            break;

        case 'embed_footer':
            // Create a modal for footer input
            const footerModal = new ModalBuilder()
                .setCustomId('embed_modal_footer')
                .setTitle('Set Embed Footer');

            const footerTextInput = new TextInputBuilder()
                .setCustomId('footerTextInput')
                .setLabel('Footer Text')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the footer text')
                .setValue(currentEmbed.footer?.text || '')
                .setRequired(false);

            const footerIconInput = new TextInputBuilder()
                .setCustomId('footerIconInput')
                .setLabel('Footer Icon URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/icon.png')
                .setValue(currentEmbed.footer?.iconURL || '')
                .setRequired(false);

            const footerTextRow = new ActionRowBuilder().addComponents(footerTextInput);
            const footerIconRow = new ActionRowBuilder().addComponents(footerIconInput);
            footerModal.addComponents(footerTextRow, footerIconRow);

            await interaction.showModal(footerModal);
            break;

        case 'embed_image':
            // Create a modal for image input
            const imageModal = new ModalBuilder()
                .setCustomId('embed_modal_image')
                .setTitle('Set Embed Image');

            const imageInput = new TextInputBuilder()
                .setCustomId('imageInput')
                .setLabel('Image URL')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/image.png')
                .setValue(currentEmbed.image?.url || '')
                .setRequired(false);

            const imageActionRow = new ActionRowBuilder().addComponents(imageInput);
            imageModal.addComponents(imageActionRow);

            await interaction.showModal(imageModal);
            break;

        case 'embed_thumbnail':
            // Create a modal for thumbnail input
            const thumbnailModal = new ModalBuilder()
                .setCustomId('embed_modal_thumbnail')
                .setTitle('Set Embed Thumbnail');

            const thumbnailInput = new TextInputBuilder()
                .setCustomId('thumbnailInput')
                .setLabel('Thumbnail URL')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/thumbnail.png')
                .setValue(currentEmbed.thumbnail?.url || '')
                .setRequired(false);

            const thumbnailActionRow = new ActionRowBuilder().addComponents(thumbnailInput);
            thumbnailModal.addComponents(thumbnailActionRow);

            await interaction.showModal(thumbnailModal);
            break;

        case 'embed_author':
            // Create a modal for author input
            const authorModal = new ModalBuilder()
                .setCustomId('embed_modal_author')
                .setTitle('Set Embed Author');

            const authorNameInput = new TextInputBuilder()
                .setCustomId('authorNameInput')
                .setLabel('Author Name')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the author name')
                .setValue(currentEmbed.author?.name || '')
                .setRequired(false);

            const authorIconInput = new TextInputBuilder()
                .setCustomId('authorIconInput')
                .setLabel('Author Icon URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/author-icon.png')
                .setValue(currentEmbed.author?.iconURL || '')
                .setRequired(false);

            const authorUrlInput = new TextInputBuilder()
                .setCustomId('authorUrlInput')
                .setLabel('Author URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com')
                .setValue(currentEmbed.author?.url || '')
                .setRequired(false);

            const authorNameRow = new ActionRowBuilder().addComponents(authorNameInput);
            const authorIconRow = new ActionRowBuilder().addComponents(authorIconInput);
            const authorUrlRow = new ActionRowBuilder().addComponents(authorUrlInput);
            authorModal.addComponents(authorNameRow, authorIconRow, authorUrlRow);

            await interaction.showModal(authorModal);
            break;

        case 'embed_timestamp':
            // Toggle timestamp
            if (currentEmbed.timestamp) {
                embedBuilder.setTimestamp(null);
            } else {
                embedBuilder.setTimestamp();
            }

            await interaction.update({
                embeds: [embedBuilder],
                components: message.components
            });
            break;

        case 'embed_field':
            // Create a modal for adding a field
            const fieldModal = new ModalBuilder()
                .setCustomId('embed_modal_field')
                .setTitle('Add Embed Field');

            const fieldNameInput = new TextInputBuilder()
                .setCustomId('fieldNameInput')
                .setLabel('Field Name')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the field name')
                .setRequired(true);

            const fieldValueInput = new TextInputBuilder()
                .setCustomId('fieldValueInput')
                .setLabel('Field Value')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Enter the field value')
                .setRequired(true);

            const fieldInlineInput = new TextInputBuilder()
                .setCustomId('fieldInlineInput')
                .setLabel('Inline (true/false)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('true or false')
                .setValue('true')
                .setRequired(true);

            const fieldNameRow = new ActionRowBuilder().addComponents(fieldNameInput);
            const fieldValueRow = new ActionRowBuilder().addComponents(fieldValueInput);
            const fieldInlineRow = new ActionRowBuilder().addComponents(fieldInlineInput);
            fieldModal.addComponents(fieldNameRow, fieldValueRow, fieldInlineRow);

            await interaction.showModal(fieldModal);
            break;

        case 'embed_remove_field':
            // If there are fields, create a select menu to choose which to remove
            if (currentEmbed.fields && currentEmbed.fields.length > 0) {
                const selectMenu = new StringSelectMenuBuilder()
                    .setCustomId('embed_select_remove_field')
                    .setPlaceholder('Select a field to remove');

                currentEmbed.fields.forEach((field, index) => {
                    selectMenu.addOptions({
                        label: field.name.substring(0, 100),
                        description: `Field #${index + 1}`,
                        value: index.toString()
                    });
                });

                const selectRow = new ActionRowBuilder().addComponents(selectMenu);

                await interaction.reply({
                    content: 'Select a field to remove:',
                    components: [selectRow],
                    ephemeral: true
                });
            } else {
                await interaction.reply({
                    content: 'This embed has no fields to remove.',
                    ephemeral: true
                });
            }
            break;

        case 'embed_preview':
            // Send a preview of the embed
            await interaction.reply({
                content: 'Here\'s a preview of your embed:',
                embeds: [embedBuilder],
                ephemeral: true
            });
            break;

        case 'embed_send':
            // Get the target channel from the original command
            const channelId = interaction.message.content.match(/channel: <#(\d+)>/)?.[1];
            let targetChannel;

            if (channelId) {
                targetChannel = interaction.guild.channels.cache.get(channelId);
            } else {
                // If we can't find the channel ID in the message, ask the user to select a channel
                const channelSelect = new StringSelectMenuBuilder()
                    .setCustomId('embed_select_channel')
                    .setPlaceholder('Select a channel to send the embed to');

                // Add text channels the user can access
                const textChannels = interaction.guild.channels.cache
                    .filter(c => c.type === 0 && c.permissionsFor(interaction.client.user).has('SendMessages'));

                if (textChannels.size === 0) {
                    return interaction.reply({
                        content: 'I don\'t have permission to send messages in any channels.',
                        ephemeral: true
                    });
                }

                // Limit to 25 channels (Discord's maximum for select menu options)
                const channelsArray = [...textChannels.values()].slice(0, 25);

                channelsArray.forEach(channel => {
                    channelSelect.addOptions({
                        label: channel.name,
                        value: channel.id
                    });
                });

                const selectRow = new ActionRowBuilder().addComponents(channelSelect);

                return interaction.reply({
                    content: 'Select a channel to send the embed to:',
                    components: [selectRow],
                    ephemeral: true
                });
            }

            // Check if we're editing an existing embed
            const editData = client.embedsBeingEdited?.get(interaction.user.id);

            if (editData) {
                try {
                    const editChannel = interaction.guild.channels.cache.get(editData.channelId);
                    const messageToEdit = await editChannel.messages.fetch(editData.messageId);

                    await messageToEdit.edit({ embeds: [embedBuilder] });

                    await interaction.reply({
                        content: `Embed updated in <#${editData.channelId}>!`,
                        ephemeral: true
                    });

                    // Clear the edit data
                    client.embedsBeingEdited.delete(interaction.user.id);
                } catch (error) {
                    console.error('Error editing embed:', error);
                    await interaction.reply({
                        content: 'There was an error editing the embed. The message may have been deleted.',
                        ephemeral: true
                    });
                }
            } else {
                // Send the embed to the target channel
                if (targetChannel) {
                    await targetChannel.send({ embeds: [embedBuilder] });

                    await interaction.reply({
                        content: `Embed sent to <#${targetChannel.id}>!`,
                        ephemeral: true
                    });
                } else {
                    await interaction.reply({
                        content: 'I couldn\'t determine which channel to send the embed to.',
                        ephemeral: true
                    });
                }
            }
            break;
    }
}

// Handle modal submissions for the embed builder
async function handleEmbedModalSubmit(interaction, client) {
    const customId = interaction.customId;
    const message = interaction.message;
    const currentEmbed = message.embeds[0];

    // Get the current embed from the message
    const embedBuilder = EmbedBuilder.from(currentEmbed);

    switch (customId) {
        case 'embed_modal_title':
            const title = interaction.fields.getTextInputValue('titleInput');
            embedBuilder.setTitle(title);
            break;

        case 'embed_modal_description':
            const description = interaction.fields.getTextInputValue('descriptionInput');
            embedBuilder.setDescription(description);
            break;

        case 'embed_modal_color':
            const color = interaction.fields.getTextInputValue('colorInput');
            try {
                embedBuilder.setColor(color);
            } catch (error) {
                return interaction.reply({
                    content: 'Invalid color format. Please use a valid HEX color code (e.g., #0099ff).',
                    ephemeral: true
                });
            }
            break;

        case 'embed_modal_footer':
            const footerText = interaction.fields.getTextInputValue('footerTextInput');
            const footerIcon = interaction.fields.getTextInputValue('footerIconInput');

            if (footerText || footerIcon) {
                embedBuilder.setFooter({
                    text: footerText || '\u200B', // Use zero-width space if no text
                    iconURL: footerIcon || null
                });
            } else {
                // If both are empty, remove the footer
                embedBuilder.setFooter(null);
            }
            break;

        case 'embed_modal_image':
            const imageUrl = interaction.fields.getTextInputValue('imageInput');

            if (imageUrl) {
                embedBuilder.setImage(imageUrl);
            } else {
                // If empty, remove the image
                embedBuilder.setImage(null);
            }
            break;

        case 'embed_modal_thumbnail':
            const thumbnailUrl = interaction.fields.getTextInputValue('thumbnailInput');

            if (thumbnailUrl) {
                embedBuilder.setThumbnail(thumbnailUrl);
            } else {
                // If empty, remove the thumbnail
                embedBuilder.setThumbnail(null);
            }
            break;

        case 'embed_modal_author':
            const authorName = interaction.fields.getTextInputValue('authorNameInput');
            const authorIcon = interaction.fields.getTextInputValue('authorIconInput');
            const authorUrl = interaction.fields.getTextInputValue('authorUrlInput');

            if (authorName) {
                embedBuilder.setAuthor({
                    name: authorName,
                    iconURL: authorIcon || null,
                    url: authorUrl || null
                });
            } else {
                // If name is empty, remove the author
                embedBuilder.setAuthor(null);
            }
            break;

        case 'embed_modal_field':
            const fieldName = interaction.fields.getTextInputValue('fieldNameInput');
            const fieldValue = interaction.fields.getTextInputValue('fieldValueInput');
            const fieldInline = interaction.fields.getTextInputValue('fieldInlineInput').toLowerCase() === 'true';

            embedBuilder.addFields({ name: fieldName, value: fieldValue, inline: fieldInline });
            break;
    }

    // Update the message with the new embed
    await interaction.update({
        embeds: [embedBuilder],
        components: message.components
    });
}

// Handle select menu interactions for the embed builder
async function handleEmbedSelectMenu(interaction, client) {
    const customId = interaction.customId;
    const values = interaction.values;

    switch (customId) {
        case 'embed_select_remove_field':
            const fieldIndex = parseInt(values[0]);
            const message = interaction.message;
            const currentEmbed = message.embeds[0];

            // Get the current embed from the message
            const embedBuilder = EmbedBuilder.from(currentEmbed);

            // Get the current fields
            const fields = [...currentEmbed.fields];

            // Remove the selected field
            fields.splice(fieldIndex, 1);

            // Set the new fields
            embedBuilder.setFields(fields);

            // Update the original builder message
            await interaction.message.interaction.editReply({
                embeds: [embedBuilder],
                components: message.message.components
            });

            // Acknowledge the selection
            await interaction.update({
                content: `Removed field #${fieldIndex + 1}`,
                components: []
            });
            break;

        case 'embed_select_channel':
            const channelId = values[0];
            const channel = interaction.guild.channels.cache.get(channelId);

            if (!channel) {
                return interaction.update({
                    content: 'Invalid channel selected.',
                    components: []
                });
            }

            // Get the embed from the original message
            const originalMessage = await interaction.message.fetchReference();
            const embed = originalMessage.embeds[0];

            // Send the embed to the selected channel
            await channel.send({ embeds: [embed] });

            // Acknowledge the selection
            await interaction.update({
                content: `Embed sent to <#${channelId}>!`,
                components: []
            });
            break;
    }
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        // Handle embed builder interactions
        if (interaction.isButton() && interaction.customId.startsWith('embed_')) {
            await handleEmbedBuilderInteraction(interaction, client);
            return;
        }

        // Handle modal submissions for embed builder
        if (interaction.isModalSubmit() && interaction.customId.startsWith('embed_modal_')) {
            await handleEmbedModalSubmit(interaction, client);
            return;
        }

        // Handle select menu interactions for embed builder
        if (interaction.isStringSelectMenu() && interaction.customId.startsWith('embed_select_')) {
            await handleEmbedSelectMenu(interaction, client);
            return;
        }

        // Handle logging setup interactions
        if (interaction.isStringSelectMenu() && interaction.customId === 'log_setup_category') {
            await handleLogSetupCategory(interaction, client);
            return;
        }

        if (interaction.isStringSelectMenu() && interaction.customId.startsWith('log_setup_channels_')) {
            await handleLogSetupChannels(interaction, client);
            return;
        }
        if (interaction.isButton()) {
            if (interaction.customId === 'server_info') {
                const guild = interaction.guild;
                const serverIconUrl = guild.iconURL({ dynamic: true, size: 1024 });
                const serverBannerUrl = guild.bannerURL({ dynamic: true, size: 4096 });

                const serverInfoEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle(`${guild.name} Information`)
                    .setThumbnail(serverIconUrl)
                    .setImage(serverBannerUrl)
                    .addFields(
                        { name: 'Server ID', value: guild.id, inline: true },
                        { name: 'Owner', value: `<@${guild.ownerId}>`, inline: true },
                        { name: 'Total Members', value: guild.memberCount.toString(), inline: true },
                        { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                        { name: 'Created On', value: guild.createdAt.toDateString(), inline: true },
                        { name: 'Server Icon URL', value: serverIconUrl ? `[Click Here](${serverIconUrl})` : 'None', inline: true },
                        { name: 'Server Banner URL', value: serverBannerUrl ? `[Click Here](${serverBannerUrl})` : 'None', inline: true }
                    )
                    .setFooter({
                        text: 'Server Information',
                        iconURL: interaction.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [serverInfoEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'rules') {
                const rulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('قوانين السيرفر')
                    .setDescription(`
                        📜 **القوانين**
                            _________________________________________
                        1. احترام جميع الأعضاء والآراء

                        2. يمنع السب والتحريض على المشاكل والمظاهرات بكل أنواعها

                        3. يمنع نشر التحذيرات التي تم ارسالها من الادارة او نشر محتوى التذاكر الخاصة بالدعم الفني في السيرفر

                        4. عدم إزعاج الإدارة إلا للضرورة

                        5. يمنع التطرق للمواضيع الخارجة عن حدود الآداب العامة أو للمواضيع الشاذة وإلخ

                        6. يمنع طلب الرتب أو التلميح لذلك , الإدارة العليا لها الحرية باختيار وإعطاء الرتب وهم اعلم بمن يستحقها

                        7. يمنع طلب المال أو أي شيء مماثل له سواء مال في لعبه أو غيرها وإلخ

                        8. يمنع التطرق للمواضيع السياسية , الدينية , العرقية , العنصرية وإلخ بأي شكل من الأشكال

                        9. يمنع وضع صورة او اسم مخل بآداب وعادات مجتمعنا

                        10. يمنع الحرق  المسلسلات والأفلام والإنمي والمباريات ما عدا البثوث

                        11. لكل شات وظيفة مخصصه , يمنع استخدام أي شات بطريقة مخالفة لوظيفته الأساسية

                        12. يمنع إعطاء حسابك لأشخاص آخرين

                        13. يمنع البيع والشراء والترويج بجميع أنواعه منعا باتا

                        14. يمنع السبام بكل أشكاله (تكرار الكلام ، الرسائل ، النقاط)

                        15. يمنع تشفير السب و التحدث او جلب سيره الاجندات مثل المثلين والمتحولين والإهانات بأي شكل من الأشكال مثل كلمة عمك ، طفل ...الخ

                        16. يمنع التدخل بالقرارات الإدارية وتعاند عليهم وتقليل من شائنهم ، فنحن أعلم بما نفعل

                        17. عدم انتحال شخصية إداري
                        __________________________________________________________________
                    `)
                    .setFooter({ text: 'آخر تحديث للقوانين' })
                    .setTimestamp();

                const rulesButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('chat_rules')
                        .setLabel('قوانين الشات')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('💬'),
                    new ButtonBuilder()
                        .setLabel('Discord Terms of Service')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://discord.com/terms')
                        .setEmoji('📜'),
                    new ButtonBuilder()
                        .setLabel('Discord Community Guidelines')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://discord.com/guidelines')
                        .setEmoji('📋')
                );

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [rulesEmbed],
                    components: [rulesButtons, backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'chat_rules') {
                const chatRulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('قوانين الشات')
                    .setDescription(`
                        💬 **قوانين الشات العامة**

                        • يمنع السب والشتم والألفاظ النابية
                        • يمنع نشر الروابط بجميع أنواعها
                        • يمنع إرسال الرسائل المكررة (السبام)
                        • يمنع استخدام منشن (@) بشكل مزعج
                        • يمنع نشر معلومات شخصية
                        • يجب احترام جميع الأعضاء والإداريين
                        • يمنع المناقشات السياسية والدينية
                        • يمنع نشر محتوى غير لائق
                        • التحدث باحترام وأدب مع الجميع
                        • استخدام القنوات المخصصة للمواضيع المختلفة
                    `)
                    .setFooter({ text: 'قوانين الشات' })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('rules')
                        .setLabel('رجوع للقوانين')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [chatRulesEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'support') {
                const supportEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('Support Information')
                    .setDescription(`
                    📋 **قوانين وتعليمات التذاكر**

            1. عند فتح تذكرة إبلاغ عضو / إداري ، يجب إحضار دليل واضح

            2. منع منعا باتا الاستهبال داخل التذكرة ايضا احترام الاداري

            3. منع فتح تذكرة بـ داعي الاستهبال او تجربة التذكرة

            4. منع ازعاج الادارة داخل التذكرة يجب عليك التحلي بـ الصبر الى حين رد الإدارة عليك

            ⚠️ **للتنبيه:**
            يحق لك فتح تذكرة بـ الاسباب التالية:
            • التبليغ على أداري او عضو
            • أستفسار عن شيء داخل السيرفر
            • في حال يوجد خلل بالرومات وما شابة داخل السيرفر تقوم بفتح تذكره

            نتمنى منكم عدم مخالفة القوانين لـ تجنب المشاكل بالسيرفر و ل يكون سيرفر نظيف خالي من المشاكل و من يخالف القوانين سوف يتعاقب

                    `)
                    .setFooter({ text: 'Support INFO' });

                const supportButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [supportEmbed],
                    components: [supportButtons],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'social_media') {
                const socialMediaEmbed = new EmbedBuilder()
                    .setColor("#61607e")
                    .setTitle("Social Media")
                    .setDescription(`
                        🌐 **Follow Us**

                        • <:icons8tiktok100:1350564524499144893>**TikTok**
                        https://tiktok.com/@ssahmx

                        • <:kick:1350897732159799346>**KICK**
                        https://kick.com/ssahm

                        • <:icons8instagram96:1350563125925249095>**Instagram**
                        https://instagram.com/5p7s

                        • <:icons8twitter100:1350565921000787968>**Twitter**
                        https://x.com/4e56

                        • <:icons8whatsapp100:1350564766560813126>**WhatsApp**
                        https://www.whatsapp.com/channel/0029Vb7oMy0HLHQeiupoGg3X

                        Stay connected with sSAHM community across all platforms!
                    `)
                    .setFooter({ text: "SH startup" });

                const socialMediaButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setLabel('TikTok')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://tiktok.com/@ssahmx')
                        .setEmoji('<:icons8tiktok100:1350564524499144893>'),
                    new ButtonBuilder()
                        .setLabel('KICK')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://kick.com/ssahm')
                        .setEmoji('<:kick:1082916486240292975>'),
                    new ButtonBuilder()
                        .setLabel('Instagram')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://instagram.com/5p7s')
                        .setEmoji('<:icons8instagram96:1350563125925249095>'),
                    new ButtonBuilder()
                        .setLabel('Twitter')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://x.com/4e56')
                        .setEmoji('<:icons8twitter100:1350565921000787968>'),
                    new ButtonBuilder()
                        .setLabel('WhatsApp')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://www.whatsapp.com/channel/0029Vb7oMy0HLHQeiupoGg3X')
                        .setEmoji('<:icons8whatsapp100:1350564766560813126>')
                );

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [socialMediaEmbed],
                    components: [socialMediaButtons, backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'back_to_main') {
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }
            else if (interaction.customId.startsWith('back_to_server_map_')) {
                // Just dismiss the message without creating a new one
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }
            else if (interaction.customId.startsWith('server_map_page_')) {
                // Just dismiss the message without creating a new one
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }
            else if (interaction.customId === 'support_info') {
                const supportEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('معلومات الدعم الفني')
                    .setDescription(`
                        📋 **قوانين وتعليمات التذاكر**
                                     **<#1348124235381342208>لطلب الدعم الفني (تكت) عليك الذهاب لروم**
                        1. عند فتح تذكرة إبلاغ عضو / إداري ، يجب إحضار دليل واضح

                        2. في حال لم يتجاوب الإداري معك او تم ظلمك يمكنك فتح تذكرة إدارة عليا او التبليغ على الاداري

                        3. منع منعا باتا الاستهبال داخل التذكرة ايضا احترام الاداري

                        4. منع فتح تذكرة بـ داعي الاستهبال او تجربة التذكرة

                        5. منع ازعاج الادارة داخل التذكرة يجب عليك التحلي بـ الصبر الى حين رد الإدارة عليك

                        ⚠️ **للتنبيه:**
                        يحق لك فتح تذكرة بـ الاسباب التالية:
                        • التبليغ على أداري او عضو
                        • أستفسار عن شيء داخل السيرفر فقط
                        • في حال يوجد خلل بالرومات وما شابة داخل السيرفر تقوم بفتح تذكره

                        📢 **ملاحظة هامة:**
                        نتمنى منكم عدم مخالفة القوانين لـ تجنب المشاكل بالسيرفر و ل يكون سيرفر نظيف خالي من المشاكل و من يخالف القوانين سوف يتعاقب
                    `)
                    .setFooter({ text: 'معلومات الدعم الفني' })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [supportEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'Level_System') {
                const ranksEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('**نظام التفاعل لـسيرفر sSAHM Community**')
                    .setDescription(`
                       **الرتب الكتابية:**

                        <@&1350906086793936916> **: level 3**
                        استعمال رياكشن على الردود

                        <@&1350906260756627499> **: level 10**
                        ارسال ايموتات والستيكرات من داخل السيرفر

                        <@&1350906373923147909> **: level 20**
                        ارسال الصور والمقاطع في الشات العام

                        <@&1350906618522632362> **: level 25**
                        ارسال الصور والفيديوهات في الرومات العامه

                        <@&1350908587907485778> **: level 30**
                        تغيير اسمك في السيرفر

                        <@&1350906795178328116> **: level 45**
                        انشاء رتبة خاصة فيك
                        + كل برمشنات رتب التفاعل

                        <@&1350906795178328116> **: level 50**
                        فتح روم الالوان

                        <@&1350906795178328116> **: level 55**
                        الخواص: جميع ما سبق

                        <@&1350906795178328116> **: level 60**
                        رتبة خاصة بلون وروم كتابي وصوتي وشارات متميزة من اختيارك خاصة بنظام القروبات القادم

                        **الرتب الصوتية:**

                        <@&1350906915697201173> **: level 3**
                        استخدام الساوند بورد من داخل السيرفر

                        <@&1350907206261805237> **: level 10**
                        استخدام الساوند بورد من خارج السيرفر

                        <@&1350907312742862883> **: level 15**
                        فتح ميزة Activities

                        <@&1350907380036010198> **: level 20**
                        فتح الشير سكرين والكام في الرومات

                        <@&1350907476039700663> **: level 25**
                        ارسال ايموتات وستيكرات بالسيرفر من خارج السيرفر ، استخدام التسجيل الصوتي أو الرسائل الصوتية بالرومات العامة

                        <@&1350907476039700663> **: level 30**
                        فتح روم الالوان

                        <@&1350907476039700663> **: level 45**
                        كل برمشنات الرتب الكتابية والصوتية

                        <@&1350906795178328116> **: level 50**
                        الخواص: جميع ما سبق

                        <@&1350906795178328116> **: level 55**
                        رول ايكون مخصص وايموجي خاص فيك

                        <@&1350906795178328116> **: level 60**
                        انشاء رتبة خاصة فيك
                        + كل برمشنات رتب التفاعل
                        + رتبة خاصة بلون وروم كتابي وصوتي وشارات متميزة من اختيارك خاصة بنظام القروبات القادم
                    `)
                    .setFooter({ text: 'SH startup' });

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [ranksEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'server_map') {
                // Show the first page of the server map
                await interaction.reply({
                    embeds: [createPageEmbed(1)],
                    components: createPageComponents(1),
                    ephemeral: true
                });
            }
            else if (interaction.customId.startsWith('server_map_next_')) {
                // Get the current page from the custom ID
                const currentPage = parseInt(interaction.customId.split('_')[3]);
                const nextPage = currentPage + 1;

                // Update the message with the next page
                await interaction.update({
                    embeds: [createPageEmbed(nextPage)],
                    components: createPageComponents(nextPage)
                });
            }
            else if (interaction.customId.startsWith('server_map_prev_')) {
                // Get the current page from the custom ID
                const currentPage = parseInt(interaction.customId.split('_')[3]);
                const prevPage = currentPage - 1;

                // Update the message with the previous page
                await interaction.update({
                    embeds: [createPageEmbed(prevPage)],
                    components: createPageComponents(prevPage)
                });
            }
        }

        // Handle select menu interactions
        if (interaction.isStringSelectMenu()) {
            // Handle channel category dropdown
            if (interaction.customId === 'server_map_channels') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'pinboard_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('📌 Pinboard Channels')
                        .setDescription(`
                           📢 **قسم الرومات المهمه في المجتمع سهم**

هذا القسم مخصص للرومات المهمه و الخاصة بالسيرفر. ستجد هنا كل ما تحتاج لمعرفته حول الرومات المهمه في المجتمع!

🎯・<#1272745870982647902>
روم المعلومات الرئيسية في السيرفر. يمكنك من خلالها الاطلاع على قواعد السيرفر، أوصاف القنوات، الأسئلة الشائعة، بالإضافة إلى معلومات التواصل والدعم إذا كنت بحاجة لأي مساعدة.

👷・<#1328257513723789342>
هنا يتم نشر جميع الأخبار الرسمية المتعلقة بالسيرفر من تحديثات وصيانات. تأكد من تفعيل التنبيهات حتى لا يفوتك أي جديد!

🎉・<#1325724553162391663>
روم الفعاليات والمسابقات! تُنشر فيه الإعلانات الخاصة بالسحوبات والمفاجآت، وأحيانًا يتم تنظيم جوائز وسحوبات مميزة للمشاركين.

🎫・<#1316945742241333256>
عند مواجهتك لأي مشكلة، أو إذا كان لديك سؤال أو استفسار، يمكنك فتح تذكرة عبر هذا الروم وسنكون سعداء بمساعدتك في أقرب وقت ممكن.
                        `)
                        .setFooter({ text: 'قنوات Pinboard' })
                        .setTimestamp();
                }
                else if (selected === 'community_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('✨ Community Channels')
                        .setDescription(`
                            # قنوات المجتمع

                             <#1272745871196426254> - الشات-العام
                            قناة للمحادثات العامة والتفاعل مع أعضاء السيرفر.

                             <#1348124235381342208> - الميديا
                            قناة لمشاركة الصور ومقاطع الفيديو والميمز.

                             <#1373135306324185098> - الأفكار-والاقتراحات
                            قناة لتقديم اقتراحات وأفكار لتحسين السيرفر.


                             <#1272745871196426254> - الشات-العام
                              لمشاركة فنكم وابداعكم بالتصوير

                             <#1272745871196426254> - الشات-العام


                             <#1272745871196426254> - الشات-العام
                            قناة للمحادثات العامة والتفاعل مع أعضاء السيرفر.


                             <#1272745871196426254> - الشات-العام
                            قناة للمحادثات العامة والتفاعل مع أعضاء السيرفر.
                        `)
                        .setFooter({ text: 'قنوات المجتمع' })
                        .setTimestamp();
                }
                else if (selected === 'hangout_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🔊 Hangout Channels')
                        .setDescription(`
                            🎧 **القسم الصوتي في سيرفر سهم**

هذا القسم مخصص للرومات الصوتية التي تتيح لك التفاعل والتواصل مع الأعضاء الآخرين بالصوت، سواء للدردشة أو لطلب الدعم أو لإنشاء رومك الخاص.

🚨・<#1316288346456195164>
إذا كنت بحاجة إلى مساعدة من الإدارة أو ترغب بالتحدث معهم صوتيًا، يمكنك الدخول إلى هذا الروم، وسيتم نقلك تلقائيًا إلى الروم المخصص عند توفر أحد الإداريين.

🔊・<#1272745871435628547> | <#1272745871435628548> | <#1316264384011440169>
هذه هي الرومات الصوتية الرئيسية الخاصة بالسيرفر. يمكنك الدخول إليها للدردشة مع أصدقائك أو التفاعل مع أعضاء المجتمع بحرية.

🔊・<#1351041982545526857>
إذا رغبت في إنشاء روم صوتي خاص بك، فقط ادخل هذا الروم وسيقوم البوت تلقائيًا بإنشاء روم صوتي مخصص لك.

⚙・<#1351041983992434798>
ملاحظة: للتحكم الكامل في إعدادات الروم الصوتي الذي تم إنشاؤه لك، توجّه إلى هذا الروم لاستخدام لوحة التحكم الخاصة بك.
                        `)
                        .setFooter({ text: 'قنوات الدردشة الصوتية' })
                        .setTimestamp();
                }

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }

            // Handle roles category dropdown
            else if (interaction.customId === 'server_map_roles') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'staff_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🛠️ Staff Roles')
                        .setDescription(`
                           🛠️ Staff Roles – رُتب الطاقم الإداري

في كل سيرفر ناجح، لا بد من وجود طاقم إداري يعمل على تنظيم المجتمع، مراقبة المحادثات، الرد على الاستفسارات، والتعامل مع المخالفين للحفاظ على بيئة آمنة ومرحّبة للجميع.
فيما يلي توضيح لأهم الرُتب الإدارية ووظائفها في سيرفر سهم:

@CEO Staff
أعلى رتبة إدارية في السيرفر، مخصصة للمسؤولين التنفيذيين والمشرفين العامين على كل ما يحدث في السيرفر.

<#1363205974554447962> | <#1325812386036191264> | <#1348827936362598490>
رُتب إدارية عُليا تُشرف على جميع أقسام السيرفر، وتقوم بمتابعة الأنشطة اليومية والإشراف الكامل على إدارة المجتمع.

@Admin | @Advisor | @Supervisor | @Mod | @Helper | @Assistant
طاقم إداري مسؤول عن الإشراف على المحادثات النصية، وتنظيم المجتمع، وتحذير أو معاقبة المخالفين عند الضرورة.

<@&1352875192484892723>
فريق خاص بالإشراف على الرد على استفسارات المتابعين، وضمان سير الأمور بسلاسة على المنصة.


فريق مسؤول عن تنظيم الفعاليات داخل السيرفر، وضمان تفاعل المجتمع والمشاركة في الأنشطة.

                        `)
                        .setFooter({ text: 'رتب الإدارة' })
                        .setTimestamp();
                }
                else if (selected === 'notification_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🔔 Notification Roles')
                        .setDescription(`
                            # رتب الإشعارات

                             ## <@&1371979813283168276>
                            إشعارات البثوث وكل شي يخص سهم

                            ## <@&1371991345794777248>
                             للحصول على إشعارات عند نشر إعلانات جديدة من تعديلات للسيرفر.

                            ## <@&1371978708197310706> | <@&1371979057289494669>
                            للحصول على إشعارات عند إقامة فعاليات جديدة.

                            ## <@&1371978050492829820>
                            تنبيهات الأذكار
                        `)
                        .setFooter({ text: 'رتب الإشعارات' })
                        .setTimestamp();
                }
                else if (selected === 'community_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('👥 Community Roles')
                        .setDescription(`
                            # رتب المجتمع

                            ## <@&1350906086793936916> - عضو مميز
                            رتبة للأعضاء النشطين والمميزين في السيرفر.

                            ## <@&1350906260756627499> - صديق
                            رتبة للأعضاء الذين يساهمون بشكل إيجابي في المجتمع.

                            ## <@&1350906373923147909> - عضو جديد
                            رتبة للأعضاء الجدد في السيرفر.
                        `)
                        .setFooter({ text: 'رتب المجتمع' })
                        .setTimestamp();
                }
                else if (selected === 'creator_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🎨 Creator Roles')
                        .setDescription(`
                            # رتب المنشئين

                            ## <@&1319920793999769611> - منشئ محتوى
                            رتبة لمنشئي المحتوى على منصات التواصل الاجتماعي.

                            ## <@&1372322714437812255> | <@&1372278730583576627> | <@&1372322952682668092> - فنان
                            رتبة للفنانين الذين يشاركون أعمالهم في السيرفر.

                            ## <@&1320607426017497220> - مصمم
                            رتبة للمصممين الذين يساهمون بتصاميمهم في السيرفر.
                        `)
                        .setFooter({ text: 'رتب المنشئين' })
                        .setTimestamp();
                }


                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }

            // Handle FAQ category dropdown
            else if (interaction.customId === 'server_map_faq') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'faq_general') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('❓ Frequently Asked Questions')
                        .setDescription(`
                         ❓ Frequently Asked Questions – الأسئلة الشائعة

الأسئلة التالية هي الأكثر تداولًا في سيرفر سهم. قمنا بجمعها مع إجاباتها ليسهُل عليك العثور على كل ما تحتاج معرفته!

✉️ **كيف يمكنني الاتصال بفريق إدارة السيرفر؟**
يمكنك التواصل مع الإدارة عبر فتح تذكرة في روم ⁠<#1316945742241333256> 🎫・ticket>، فقط اضغط على الزر المخصص، وسيتم فتح تذكرة خاصة بك وسيتواصل معك أحد أفراد الفريق.


🔑 **كيف أحصل على الرولات؟**
 (channel browser) ببساطة، توجّه إلى قسم القنوات والأدوار أعلى السيرفر، أو اضغط على الزر الموجود في الروم المخصص لاختيار الرولات حسب اهتماماتك.

🏰 **كيف أُظهر فقط القنوات التي تهمني؟**
لتخصيص قائمة القنوات التي تراها، ادخل إلى قسم استعراض القنوات (channel browser) وحدد القنوات التي تريد إظهارها فقط.

🏆 **ما هو المستوى وكيف أزيده؟**
نظام المستوى يعكس مدى نشاطك في السيرفر. كلما كنت أكثر تفاعلاً في الشات أو الصوت، زاد مستواك وظهرت أعلى في قائمة الأعضاء، وقد تُفتح لك مزايا إضافية.
استخدم الأمر /rank في روم الأوامر لرؤية مستواك الحالي وملفك.

🎁 **كيف أشارك في السحوبات (Giveaways)؟**
جميع السحوبات والإعلانات عنها تتم في روم <⁠#1272745870982647908>. تأكد من المتابعة هناك وقد تكون أنت الفائز القادم! 🎉

❤️ **ما فائدة البوست (Boost)؟**
الـ Boost يُعتبر دعمًا مباشرًا للسيرفر، يُساعدنا على تقديم جوائز أكثر، فعاليات مميزة، وخدمات أفضل. ونحن بدورنا نُقدّر دعمك كثيرًا ❤️
                        `)
                        .setFooter({ text: 'Frequently Asked Questions' })
                        .setTimestamp();
                }


                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }
        }

        // Handle custom command modal submissions
        if (interaction.isModalSubmit()) {
            if (interaction.customId === 'create_command_modal') {
                await handleCreateCommandModal(interaction, client);
                return;
            } else if (interaction.customId.startsWith('edit_command_modal_')) {
                await handleEditCommandModal(interaction, client);
                return;
            }
        }

        // Handle autocomplete for custom commands
        if (interaction.isAutocomplete()) {
            const command = client.commands.get(interaction.commandName);
            if (command && command.autocomplete) {
                try {
                    await command.autocomplete(interaction);
                } catch (error) {
                    console.error('Error handling autocomplete:', error);
                }
            }
            return;
        }

        if (interaction.type === InteractionType.ApplicationCommand) {
            const command = client.commands.get(interaction.commandName);

            // If it's not a regular command, check if it's a custom command
            if (!command) {
                if (client.handleCustomCommands && client.handleCustomCommands.isCustomCommand(interaction.commandName)) {
                    try {
                        await client.handleCustomCommands.executeCustomCommand(interaction);
                    } catch (error) {
                        console.error('Error executing custom command:', error);
                        await interaction.reply({
                            content: "There was an error while executing this custom command!",
                            ephemeral: true
                        });
                    }
                }
                return;
            }

            try {
                await command.execute(interaction, client);
            } catch (error) {
                console.error(error);
                await interaction.reply({
                    content: "There was an error while executing this command!",
                    ephemeral: true
                });
            }
        }
    }
};

// Handle create command modal submission
async function handleCreateCommandModal(interaction, client) {
    const commandName = interaction.fields.getTextInputValue('command_name').toLowerCase().trim();
    const commandDescription = interaction.fields.getTextInputValue('command_description').trim();
    const commandResponse = interaction.fields.getTextInputValue('command_response').trim();

    // Validate command name
    if (!/^[a-z0-9_-]{1,32}$/.test(commandName)) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Invalid Command Name')
            .setDescription('Command name must be 1-32 characters long and contain only lowercase letters, numbers, hyphens, and underscores.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Check if user has permission to create commands (optional - you can modify this)
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to create custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    try {
        const result = await client.handleCustomCommands.createCustomCommand(
            commandName,
            commandDescription,
            commandResponse,
            interaction.user.id
        );

        const embed = new EmbedBuilder()
            .setColor(result.success ? '#4CAF50' : '#ff6b6b')
            .setTitle(result.success ? '✅ Command Created' : '❌ Creation Failed')
            .setDescription(result.message)
            .setTimestamp();

        if (result.success) {
            embed.addFields({
                name: 'Command Details',
                value: `**Name:** \`/${commandName}\`\n**Description:** ${commandDescription}\n**Response:** ${commandResponse.substring(0, 100)}${commandResponse.length > 100 ? '...' : ''}`,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        console.error('Error creating custom command:', error);
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Error')
            .setDescription('An error occurred while creating the custom command.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

// Handle edit command modal submission
async function handleEditCommandModal(interaction, client) {
    const commandName = interaction.customId.replace('edit_command_modal_', '');
    const commandDescription = interaction.fields.getTextInputValue('command_description').trim();
    const commandResponse = interaction.fields.getTextInputValue('command_response').trim();

    try {
        const result = await client.handleCustomCommands.updateCustomCommand(
            commandName,
            commandDescription,
            commandResponse,
            interaction.user.id
        );

        const embed = new EmbedBuilder()
            .setColor(result.success ? '#4CAF50' : '#ff6b6b')
            .setTitle(result.success ? '✅ Command Updated' : '❌ Update Failed')
            .setDescription(result.message)
            .setTimestamp();

        if (result.success) {
            embed.addFields({
                name: 'Updated Command Details',
                value: `**Name:** \`/${commandName}\`\n**Description:** ${commandDescription}\n**Response:** ${commandResponse.substring(0, 100)}${commandResponse.length > 100 ? '...' : ''}`,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        console.error('Error updating custom command:', error);
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Error')
            .setDescription('An error occurred while updating the custom command.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

// Handle logging setup category selection
async function handleLogSetupCategory(interaction, client) {
    const category = interaction.values[0];
    const guildId = interaction.guild.id;

    if (category === 'view_settings') {
        await showCurrentLogSettings(interaction, client);
        return;
    }

    const categoryInfo = {
        'message_events': {
            title: '📝 Message Events Configuration',
            description: 'Configure logging for message-related events',
            options: [
                { label: 'Message Deletions', value: 'message_delete', description: 'Log when messages are deleted' },
                { label: 'Message Edits', value: 'message_edit', description: 'Log when messages are edited' }
            ]
        },
        'member_events': {
            title: '👥 Member Events Configuration',
            description: 'Configure logging for member-related events',
            options: [
                { label: 'Member Joins', value: 'member_join', description: 'Log when members join the server' },
                { label: 'Member Leaves', value: 'member_leave', description: 'Log when members leave the server' }
            ]
        },
        'moderation_events': {
            title: '🔨 Moderation Events Configuration',
            description: 'Configure logging for moderation actions',
            options: [
                { label: 'Bans', value: 'ban', description: 'Log when members are banned' },
                { label: 'Unbans', value: 'unban', description: 'Log when members are unbanned' },
                { label: 'Kicks', value: 'kick', description: 'Log when members are kicked' },
                { label: 'Timeouts', value: 'timeout', description: 'Log when members are timed out' },
                { label: 'All Moderation', value: 'moderation', description: 'Log all moderation actions' }
            ]
        },
        'server_events': {
            title: '⚙️ Server Events Configuration',
            description: 'Configure logging for server changes',
            options: [
                { label: 'Role Changes', value: 'role_change', description: 'Log when member roles are changed' },
                { label: 'Channel Changes', value: 'channel_change', description: 'Log when channels are modified' }
            ]
        },
        'voice_events': {
            title: '🔊 Voice Events Configuration',
            description: 'Configure logging for voice channel activity',
            options: [
                { label: 'Voice Activity', value: 'voice_activity', description: 'Log voice channel joins/leaves' }
            ]
        }
    };

    const info = categoryInfo[category];
    if (!info) return;

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle(info.title)
        .setDescription(`${info.description}\n\nSelect the event types you want to configure:`)
        .setFooter({ text: 'Select an event type to choose its logging channel' })
        .setTimestamp();

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`log_setup_channels_${category}`)
        .setPlaceholder('Choose an event type to configure')
        .addOptions(info.options);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.update({
        embeds: [embed],
        components: [row]
    });
}

// Handle logging channel selection
async function handleLogSetupChannels(interaction, client) {
    const category = interaction.customId.split('_')[3];
    const logType = interaction.values[0];
    const guildId = interaction.guild.id;

    // Get all text channels in the guild
    const textChannels = interaction.guild.channels.cache
        .filter(channel => channel.type === 0) // Text channels
        .map(channel => ({
            label: `#${channel.name}`,
            value: channel.id,
            description: `Set as logging channel for ${getLogTypeDisplayName(logType)}`
        }))
        .slice(0, 25); // Discord limit

    if (textChannels.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ No Text Channels Found')
            .setDescription('No text channels found in this server.')
            .setTimestamp();

        return interaction.update({ embeds: [embed], components: [] });
    }

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle(`📋 Configure ${getLogTypeDisplayName(logType)}`)
        .setDescription(`Select a channel where ${getLogTypeDisplayName(logType).toLowerCase()} should be logged:`)
        .setFooter({ text: 'Choose a text channel from the dropdown below' })
        .setTimestamp();

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`log_channel_select_${logType}`)
        .setPlaceholder('Choose a logging channel')
        .addOptions(textChannels);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.update({
        embeds: [embed],
        components: [row]
    });

    // Set up collector for channel selection
    const collector = interaction.message.createMessageComponentCollector({
        time: 60000 // 1 minute
    });

    collector.on('collect', async (i) => {
        if (i.user.id !== interaction.user.id) {
            return i.reply({ content: 'You cannot configure logging settings.', ephemeral: true });
        }

        if (i.customId === `log_channel_select_${logType}`) {
            const channelId = i.values[0];
            const channel = interaction.guild.channels.cache.get(channelId);

            // Save the setting
            const logSettings = client.handleLogging ? client.handleLogging.loadLogSettings() : {};
            if (!logSettings[guildId]) {
                logSettings[guildId] = {};
            }
            logSettings[guildId][logType] = channelId;

            if (client.handleLogging && client.handleLogging.saveLogSettings(logSettings)) {
                const successEmbed = new EmbedBuilder()
                    .setColor('#4CAF50')
                    .setTitle('✅ Logging Configured')
                    .setDescription(`Successfully configured logging for **${getLogTypeDisplayName(logType)}**`)
                    .addFields({
                        name: 'Channel',
                        value: `${channel.name} (${channel.id})`,
                        inline: true
                    })
                    .setFooter({ text: 'Use /logs setup to configure more event types' })
                    .setTimestamp();

                await i.update({ embeds: [successEmbed], components: [] });
            } else {
                const errorEmbed = new EmbedBuilder()
                    .setColor('#ff6b6b')
                    .setTitle('❌ Configuration Failed')
                    .setDescription('Failed to save logging configuration. Please try again.')
                    .setTimestamp();

                await i.update({ embeds: [errorEmbed], components: [] });
            }

            collector.stop();
        }
    });

    collector.on('end', async (collected) => {
        if (collected.size === 0) {
            try {
                const timeoutEmbed = new EmbedBuilder()
                    .setColor('#6c757d')
                    .setTitle('⏰ Configuration Timeout')
                    .setDescription('Logging configuration timed out. Please try again.')
                    .setTimestamp();

                await interaction.editReply({ embeds: [timeoutEmbed], components: [] });
            } catch (error) {
                // Message might have been deleted
            }
        }
    });
}

// Show current log settings
async function showCurrentLogSettings(interaction, client) {
    const guildId = interaction.guild.id;
    const logSettings = client.handleLogging ? client.handleLogging.loadLogSettings() : {};
    const guildSettings = logSettings[guildId] || {};

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle('👁️ Current Logging Configuration')
        .setTimestamp();

    if (Object.keys(guildSettings).length === 0) {
        embed.setDescription('No logging is currently configured for this server.');
    } else {
        let description = 'Current logging configuration:\n\n';

        for (const [logType, channelId] of Object.entries(guildSettings)) {
            const channel = interaction.guild.channels.cache.get(channelId);
            const channelName = channel ? `#${channel.name}` : `Unknown Channel (${channelId})`;
            description += `**${getLogTypeDisplayName(logType)}:** ${channelName}\n`;
        }

        embed.setDescription(description);
    }

    embed.setFooter({ text: 'Use /logs setup to modify configuration' });

    await interaction.update({ embeds: [embed], components: [] });
}

// Helper function to get display name for log types
function getLogTypeDisplayName(type) {
    const displayNames = {
        'message_delete': 'Message Deletions',
        'message_edit': 'Message Edits',
        'member_join': 'Member Joins',
        'member_leave': 'Member Leaves',
        'ban': 'Bans',
        'unban': 'Unbans',
        'kick': 'Kicks',
        'timeout': 'Timeouts',
        'moderation': 'All Moderation',
        'role_change': 'Role Changes',
        'channel_change': 'Channel Changes',
        'voice_activity': 'Voice Activity'
    };

    return displayNames[type] || type;
}