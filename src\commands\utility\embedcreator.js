const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON>bedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('embedcreator')
        .setDescription('Create and manage custom embeds')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a custom embed with an interactive builder')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('The channel to send the embed to')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('edit')
                .setDescription('Edit an existing embed')
                .addStringOption(option =>
                    option.setName('message_id')
                        .setDescription('The ID of the message containing the embed to edit')
                        .setRequired(true))
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('The channel where the message is located')
                        .setRequired(true))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'create') {
            await handleCreateEmbed(interaction);
        } else if (subcommand === 'edit') {
            await handleEditEmbed(interaction);
        }
    }
};

// Initial embed template
function createInitialEmbed() {
    return new EmbedBuilder()
        .setColor('#0099ff')
        .setTitle('New Embed')
        .setDescription('Use the buttons below to customize this embed')
        .setFooter({ text: 'Created with Embed Builder' })
        .setTimestamp();
}

// Create the builder buttons
function createBuilderComponents() {
    const row1 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_title')
            .setLabel('Set Title')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_description')
            .setLabel('Set Description')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_color')
            .setLabel('Set Color')
            .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
            .setCustomId('embed_footer')
            .setLabel('Set Footer')
            .setStyle(ButtonStyle.Primary)
    );

    const row2 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_image')
            .setLabel('Set Image')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_thumbnail')
            .setLabel('Set Thumbnail')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_author')
            .setLabel('Set Author')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_timestamp')
            .setLabel('Toggle Timestamp')
            .setStyle(ButtonStyle.Secondary)
    );

    const row3 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('embed_field')
            .setLabel('Add Field')
            .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
            .setCustomId('embed_remove_field')
            .setLabel('Remove Field')
            .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
            .setCustomId('embed_preview')
            .setLabel('Preview')
            .setStyle(ButtonStyle.Secondary),
        new ButtonBuilder()
            .setCustomId('embed_send')
            .setLabel('Send Embed')
            .setStyle(ButtonStyle.Success)
    );

    return [row1, row2, row3];
}

// Handle the create embed command
async function handleCreateEmbed(interaction) {
    const channel = interaction.options.getChannel('channel');

    // Check if the bot has permission to send messages in the channel
    if (!channel.permissionsFor(interaction.client.user).has('SendMessages')) {
        return interaction.reply({
            content: 'I don\'t have permission to send messages in that channel!',
            ephemeral: true
        });
    }

    const initialEmbed = createInitialEmbed();
    const components = createBuilderComponents();

    await interaction.reply({
        content: 'Use the buttons below to customize your embed:',
        embeds: [initialEmbed],
        components: components,
        ephemeral: true
    });
}

// Handle the edit embed command
async function handleEditEmbed(interaction) {
    const messageId = interaction.options.getString('message_id');
    const channel = interaction.options.getChannel('channel');

    try {
        // Fetch the message to edit
        const message = await channel.messages.fetch(messageId);

        // Check if the message is from the bot and has an embed
        if (message.author.id !== interaction.client.user.id || !message.embeds.length) {
            return interaction.reply({
                content: 'I can only edit embeds that I\'ve sent!',
                ephemeral: true
            });
        }

        // Get the existing embed
        const existingEmbed = EmbedBuilder.from(message.embeds[0]);
        const components = createBuilderComponents();

        await interaction.reply({
            content: 'Editing existing embed:',
            embeds: [existingEmbed],
            components: components,
            ephemeral: true
        });

        // Store the message ID and channel ID for later use
        interaction.client.embedsBeingEdited = interaction.client.embedsBeingEdited || new Map();
        interaction.client.embedsBeingEdited.set(interaction.user.id, {
            messageId: messageId,
            channelId: channel.id
        });

    } catch (error) {
        console.error('Error fetching message to edit:', error);
        return interaction.reply({
            content: 'I couldn\'t find that message. Make sure the message ID is correct and that the message is in the specified channel.',
            ephemeral: true
        });
    }
}
