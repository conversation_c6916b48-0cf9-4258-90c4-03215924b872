const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, <PERSON>bed<PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder, ChannelType, PermissionFlagsBits } = require('discord.js');
const fs = require('fs');
const path = require('path');

const LOG_SETTINGS_FILE = path.join(__dirname, '../../data/logSettings.json');
const AUDIT_LOGS_FILE = path.join(__dirname, '../../data/auditLogs.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('logs')
        .setDescription('Configure and manage server logging')
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand =>
            subcommand
                .setName('setup')
                .setDescription('Configure logging channels for different event types'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('View logs of a specific type')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('The type of logs to view')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Message Deletions', value: 'message_delete' },
                            { name: 'Message Edits', value: 'message_edit' },
                            { name: 'Member Joins', value: 'member_join' },
                            { name: 'Member Leaves', value: 'member_leave' },
                            { name: 'Bans', value: 'ban' },
                            { name: 'Unbans', value: 'unban' },
                            { name: 'Kicks', value: 'kick' },
                            { name: 'Timeouts', value: 'timeout' },
                            { name: 'Role Changes', value: 'role_change' },
                            { name: 'Channel Changes', value: 'channel_change' },
                            { name: 'Voice Activity', value: 'voice_activity' },
                            { name: 'All Events', value: 'all' }
                        ))
                .addIntegerOption(option =>
                    option.setName('limit')
                        .setDescription('Number of logs to display (1-50)')
                        .setMinValue(1)
                        .setMaxValue(50)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('disable')
                .setDescription('Disable logging for specific event types')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('The type of logging to disable')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Message Deletions', value: 'message_delete' },
                            { name: 'Message Edits', value: 'message_edit' },
                            { name: 'Member Joins', value: 'member_join' },
                            { name: 'Member Leaves', value: 'member_leave' },
                            { name: 'Moderation Actions', value: 'moderation' },
                            { name: 'Role Changes', value: 'role_change' },
                            { name: 'Channel Changes', value: 'channel_change' },
                            { name: 'Voice Activity', value: 'voice_activity' },
                            { name: 'All Logging', value: 'all' }
                        ))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'setup':
                await handleSetupLogs(interaction);
                break;
            case 'view':
                await handleViewLogs(interaction);
                break;
            case 'disable':
                await handleDisableLogs(interaction);
                break;
        }
    }
};

// Load log settings from JSON file
function loadLogSettings() {
    try {
        if (!fs.existsSync(LOG_SETTINGS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(LOG_SETTINGS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading log settings:', error);
        return {};
    }
}

// Save log settings to JSON file
function saveLogSettings(settings) {
    try {
        const dataDir = path.dirname(LOG_SETTINGS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        
        fs.writeFileSync(LOG_SETTINGS_FILE, JSON.stringify(settings, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving log settings:', error);
        return false;
    }
}

// Load audit logs from JSON file
function loadAuditLogs() {
    try {
        if (!fs.existsSync(AUDIT_LOGS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(AUDIT_LOGS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading audit logs:', error);
        return {};
    }
}

// Handle setup logs command
async function handleSetupLogs(interaction) {
    const guildId = interaction.guild.id;
    const logSettings = loadLogSettings();
    
    if (!logSettings[guildId]) {
        logSettings[guildId] = {};
    }

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle('📋 Logging System Setup')
        .setDescription('Select the types of events you want to log and configure their channels.')
        .addFields(
            {
                name: '📝 Available Log Types',
                value: `
                **Message Events:**
                • Message Deletions
                • Message Edits
                
                **Member Events:**
                • Member Joins
                • Member Leaves
                
                **Moderation Events:**
                • Bans & Unbans
                • Kicks & Timeouts
                
                **Server Events:**
                • Role Changes
                • Channel Changes
                • Voice Activity
                `,
                inline: false
            }
        )
        .setFooter({ text: 'Select a category below to configure channels' })
        .setTimestamp();

    const selectMenu = new StringSelectMenuBuilder()
        .setCustomId('log_setup_category')
        .setPlaceholder('Choose a log category to configure')
        .addOptions([
            {
                label: 'Message Events',
                description: 'Configure logging for message deletions and edits',
                value: 'message_events',
                emoji: '📝'
            },
            {
                label: 'Member Events',
                description: 'Configure logging for member joins and leaves',
                value: 'member_events',
                emoji: '👥'
            },
            {
                label: 'Moderation Events',
                description: 'Configure logging for bans, kicks, timeouts',
                value: 'moderation_events',
                emoji: '🔨'
            },
            {
                label: 'Server Events',
                description: 'Configure logging for role and channel changes',
                value: 'server_events',
                emoji: '⚙️'
            },
            {
                label: 'Voice Events',
                description: 'Configure logging for voice channel activity',
                value: 'voice_events',
                emoji: '🔊'
            },
            {
                label: 'View Current Settings',
                description: 'See current logging configuration',
                value: 'view_settings',
                emoji: '👁️'
            }
        ]);

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
}

// Handle view logs command
async function handleViewLogs(interaction) {
    const logType = interaction.options.getString('type');
    const limit = interaction.options.getInteger('limit') || 10;
    const guildId = interaction.guild.id;

    await interaction.deferReply({ ephemeral: true });

    const auditLogs = loadAuditLogs();
    const guildLogs = auditLogs[guildId] || {};
    
    let logsToShow = [];
    
    if (logType === 'all') {
        // Get all logs and sort by timestamp
        for (const [type, logs] of Object.entries(guildLogs)) {
            if (Array.isArray(logs)) {
                logsToShow.push(...logs.map(log => ({ ...log, type })));
            }
        }
        logsToShow.sort((a, b) => b.timestamp - a.timestamp);
    } else {
        logsToShow = guildLogs[logType] || [];
        logsToShow.sort((a, b) => b.timestamp - a.timestamp);
    }

    if (logsToShow.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('📋 No Logs Found')
            .setDescription(`No logs found for type: **${logType}**`)
            .setTimestamp();

        return interaction.editReply({ embeds: [embed] });
    }

    // Limit the results
    const limitedLogs = logsToShow.slice(0, limit);

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle(`📋 ${logType === 'all' ? 'All Logs' : getLogTypeDisplayName(logType)}`)
        .setDescription(`Showing ${limitedLogs.length} of ${logsToShow.length} logs`)
        .setFooter({ text: `Page 1 • Use /audit <user> for user-specific logs` })
        .setTimestamp();

    // Add log entries as fields
    limitedLogs.forEach((log, index) => {
        const logTypeDisplay = logType === 'all' ? `[${getLogTypeDisplayName(log.type)}] ` : '';
        const timestamp = `<t:${Math.floor(log.timestamp / 1000)}:R>`;
        
        embed.addFields({
            name: `${logTypeDisplay}${log.action || 'Event'}`,
            value: `${log.description}\n**Time:** ${timestamp}\n**User:** <@${log.userId}>${log.moderator ? `\n**Moderator:** <@${log.moderator}>` : ''}`,
            inline: false
        });
    });

    await interaction.editReply({ embeds: [embed] });
}

// Handle disable logs command
async function handleDisableLogs(interaction) {
    const logType = interaction.options.getString('type');
    const guildId = interaction.guild.id;
    const logSettings = loadLogSettings();

    if (!logSettings[guildId]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ No Logging Configured')
            .setDescription('No logging has been set up for this server yet. Use `/logs setup` first.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    if (logType === 'all') {
        delete logSettings[guildId];
    } else {
        delete logSettings[guildId][logType];
    }

    if (saveLogSettings(logSettings)) {
        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('✅ Logging Disabled')
            .setDescription(`Successfully disabled logging for: **${getLogTypeDisplayName(logType)}**`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } else {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Error')
            .setDescription('Failed to disable logging. Please try again.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

// Helper function to get display name for log types
function getLogTypeDisplayName(type) {
    const displayNames = {
        'message_delete': 'Message Deletions',
        'message_edit': 'Message Edits',
        'member_join': 'Member Joins',
        'member_leave': 'Member Leaves',
        'ban': 'Bans',
        'unban': 'Unbans',
        'kick': 'Kicks',
        'timeout': 'Timeouts',
        'role_change': 'Role Changes',
        'channel_change': 'Channel Changes',
        'voice_activity': 'Voice Activity',
        'all': 'All Events'
    };
    
    return displayNames[type] || type;
}
