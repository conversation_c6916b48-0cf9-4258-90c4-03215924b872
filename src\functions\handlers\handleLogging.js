const { Embed<PERSON><PERSON><PERSON>, AuditLogEvent } = require('discord.js');
const fs = require('fs');
const path = require('path');

const LOG_SETTINGS_FILE = path.join(__dirname, '../../data/logSettings.json');
const AUDIT_LOGS_FILE = path.join(__dirname, '../../data/auditLogs.json');

module.exports = (client) => {
    client.handleLogging = {
        // Load log settings from JSON file
        loadLogSettings() {
            try {
                if (!fs.existsSync(LOG_SETTINGS_FILE)) {
                    return {};
                }
                const data = fs.readFileSync(LOG_SETTINGS_FILE, 'utf8');
                return JSON.parse(data);
            } catch (error) {
                console.error('Error loading log settings:', error);
                return {};
            }
        },

        // Save log settings to JSON file
        saveLogSettings(settings) {
            try {
                const dataDir = path.dirname(LOG_SETTINGS_FILE);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }
                
                fs.writeFileSync(LOG_SETTINGS_FILE, JSON.stringify(settings, null, 2));
                return true;
            } catch (error) {
                console.error('Error saving log settings:', error);
                return false;
            }
        },

        // Load audit logs from JSON file
        loadAuditLogs() {
            try {
                if (!fs.existsSync(AUDIT_LOGS_FILE)) {
                    return {};
                }
                const data = fs.readFileSync(AUDIT_LOGS_FILE, 'utf8');
                return JSON.parse(data);
            } catch (error) {
                console.error('Error loading audit logs:', error);
                return {};
            }
        },

        // Save audit logs to JSON file
        saveAuditLogs(logs) {
            try {
                const dataDir = path.dirname(AUDIT_LOGS_FILE);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }
                
                fs.writeFileSync(AUDIT_LOGS_FILE, JSON.stringify(logs, null, 2));
                return true;
            } catch (error) {
                console.error('Error saving audit logs:', error);
                return false;
            }
        },

        // Add a log entry
        addLogEntry(guildId, logType, logData) {
            const auditLogs = this.loadAuditLogs();
            
            if (!auditLogs[guildId]) {
                auditLogs[guildId] = {};
            }
            
            if (!auditLogs[guildId][logType]) {
                auditLogs[guildId][logType] = [];
            }

            // Add timestamp if not provided
            if (!logData.timestamp) {
                logData.timestamp = Date.now();
            }

            auditLogs[guildId][logType].push(logData);

            // Keep only last 1000 entries per type to prevent file from getting too large
            if (auditLogs[guildId][logType].length > 1000) {
                auditLogs[guildId][logType] = auditLogs[guildId][logType].slice(-1000);
            }

            this.saveAuditLogs(auditLogs);
        },

        // Send log to configured channel
        async sendLog(guild, logType, embed) {
            const logSettings = this.loadLogSettings();
            const guildSettings = logSettings[guild.id];

            if (!guildSettings || !guildSettings[logType]) {
                return; // No logging configured for this type
            }

            const channelId = guildSettings[logType];
            const channel = guild.channels.cache.get(channelId);

            if (!channel) {
                console.warn(`Log channel ${channelId} not found for guild ${guild.id}`);
                return;
            }

            try {
                await channel.send({ embeds: [embed] });
            } catch (error) {
                console.error(`Error sending log to channel ${channelId}:`, error);
            }
        },

        // Log message deletion
        async logMessageDelete(message) {
            if (message.author.bot) return; // Don't log bot messages

            const embed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('🗑️ Message Deleted')
                .addFields(
                    { name: 'Author', value: `${message.author.tag} (${message.author.id})`, inline: true },
                    { name: 'Channel', value: `${message.channel.name} (${message.channel.id})`, inline: true },
                    { name: 'Message ID', value: message.id, inline: true },
                    { name: 'Content', value: message.content || '*No text content*', inline: false }
                )
                .setTimestamp();

            // Add to audit logs
            this.addLogEntry(message.guild.id, 'message_delete', {
                userId: message.author.id,
                action: 'Message Deleted',
                description: `Message deleted in #${message.channel.name}`,
                messageContent: message.content,
                channelId: message.channel.id,
                messageId: message.id
            });

            await this.sendLog(message.guild, 'message_delete', embed);
        },

        // Log message edit
        async logMessageEdit(oldMessage, newMessage) {
            if (oldMessage.author.bot) return; // Don't log bot messages
            if (oldMessage.content === newMessage.content) return; // No actual content change

            const embed = new EmbedBuilder()
                .setColor('#ffa500')
                .setTitle('✏️ Message Edited')
                .addFields(
                    { name: 'Author', value: `${newMessage.author.tag} (${newMessage.author.id})`, inline: true },
                    { name: 'Channel', value: `${newMessage.channel.name} (${newMessage.channel.id})`, inline: true },
                    { name: 'Message ID', value: newMessage.id, inline: true },
                    { name: 'Before', value: oldMessage.content || '*No text content*', inline: false },
                    { name: 'After', value: newMessage.content || '*No text content*', inline: false }
                )
                .setTimestamp();

            // Add to audit logs
            this.addLogEntry(newMessage.guild.id, 'message_edit', {
                userId: newMessage.author.id,
                action: 'Message Edited',
                description: `Message edited in #${newMessage.channel.name}`,
                oldContent: oldMessage.content,
                newContent: newMessage.content,
                channelId: newMessage.channel.id,
                messageId: newMessage.id
            });

            await this.sendLog(newMessage.guild, 'message_edit', embed);
        },

        // Log member join
        async logMemberJoin(member) {
            const embed = new EmbedBuilder()
                .setColor('#4CAF50')
                .setTitle('📥 Member Joined')
                .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
                .addFields(
                    { name: 'User', value: `${member.user.tag} (${member.user.id})`, inline: true },
                    { name: 'Account Created', value: `<t:${Math.floor(member.user.createdTimestamp / 1000)}:R>`, inline: true },
                    { name: 'Member Count', value: member.guild.memberCount.toString(), inline: true }
                )
                .setTimestamp();

            // Add to audit logs
            this.addLogEntry(member.guild.id, 'member_join', {
                userId: member.user.id,
                action: 'Member Joined',
                description: `${member.user.tag} joined the server`,
                accountAge: Date.now() - member.user.createdTimestamp
            });

            await this.sendLog(member.guild, 'member_join', embed);
        },

        // Log member leave
        async logMemberLeave(member) {
            const embed = new EmbedBuilder()
                .setColor('#ff6b6b')
                .setTitle('📤 Member Left')
                .setThumbnail(member.user.displayAvatarURL({ dynamic: true }))
                .addFields(
                    { name: 'User', value: `${member.user.tag} (${member.user.id})`, inline: true },
                    { name: 'Joined', value: member.joinedAt ? `<t:${Math.floor(member.joinedTimestamp / 1000)}:R>` : 'Unknown', inline: true },
                    { name: 'Member Count', value: member.guild.memberCount.toString(), inline: true }
                )
                .setTimestamp();

            // Add to audit logs
            this.addLogEntry(member.guild.id, 'member_leave', {
                userId: member.user.id,
                action: 'Member Left',
                description: `${member.user.tag} left the server`,
                timeInServer: member.joinedAt ? Date.now() - member.joinedTimestamp : null
            });

            await this.sendLog(member.guild, 'member_leave', embed);
        },

        // Log moderation action
        async logModerationAction(guild, action, target, moderator, reason, duration = null) {
            const actionColors = {
                'ban': '#8B0000',
                'unban': '#4CAF50',
                'kick': '#FF4500',
                'timeout': '#FFA500',
                'untimeout': '#4CAF50'
            };

            const actionEmojis = {
                'ban': '🔨',
                'unban': '🔓',
                'kick': '👢',
                'timeout': '⏰',
                'untimeout': '⏰'
            };

            const embed = new EmbedBuilder()
                .setColor(actionColors[action] || '#666666')
                .setTitle(`${actionEmojis[action] || '⚖️'} ${action.charAt(0).toUpperCase() + action.slice(1)}`)
                .addFields(
                    { name: 'Target', value: `${target.tag} (${target.id})`, inline: true },
                    { name: 'Moderator', value: `${moderator.tag} (${moderator.id})`, inline: true }
                );

            if (reason) {
                embed.addFields({ name: 'Reason', value: reason, inline: false });
            }

            if (duration) {
                embed.addFields({ name: 'Duration', value: duration, inline: true });
            }

            embed.setTimestamp();

            // Add to audit logs
            this.addLogEntry(guild.id, action, {
                userId: target.id,
                targetId: target.id,
                moderator: moderator.id,
                action: action.charAt(0).toUpperCase() + action.slice(1),
                description: `${target.tag} was ${action}${moderator ? ` by ${moderator.tag}` : ''}`,
                reason: reason,
                duration: duration
            });

            await this.sendLog(guild, 'moderation', embed);
        },

        // Log role change
        async logRoleChange(member, oldRoles, newRoles) {
            const addedRoles = newRoles.filter(role => !oldRoles.has(role.id));
            const removedRoles = oldRoles.filter(role => !newRoles.has(role.id));

            if (addedRoles.length === 0 && removedRoles.length === 0) return;

            const embed = new EmbedBuilder()
                .setColor('#9C27B0')
                .setTitle('🎭 Role Change')
                .addFields(
                    { name: 'User', value: `${member.user.tag} (${member.user.id})`, inline: true }
                );

            if (addedRoles.length > 0) {
                embed.addFields({ name: 'Roles Added', value: addedRoles.map(role => role.name).join(', '), inline: false });
            }

            if (removedRoles.length > 0) {
                embed.addFields({ name: 'Roles Removed', value: removedRoles.map(role => role.name).join(', '), inline: false });
            }

            embed.setTimestamp();

            // Add to audit logs
            this.addLogEntry(member.guild.id, 'role_change', {
                userId: member.user.id,
                action: 'Role Change',
                description: `Roles changed for ${member.user.tag}`,
                addedRoles: addedRoles.map(role => ({ id: role.id, name: role.name })),
                removedRoles: removedRoles.map(role => ({ id: role.id, name: role.name }))
            });

            await this.sendLog(member.guild, 'role_change', embed);
        }
    };

    // Initialize logging event listeners
    client.handleLogging.initializeEventListeners = () => {
        // Message events
        client.on('messageDelete', (message) => {
            if (message.partial || !message.guild) return;
            client.handleLogging.logMessageDelete(message);
        });

        client.on('messageUpdate', (oldMessage, newMessage) => {
            if (oldMessage.partial || newMessage.partial || !newMessage.guild) return;
            client.handleLogging.logMessageEdit(oldMessage, newMessage);
        });

        // Member events
        client.on('guildMemberAdd', (member) => {
            client.handleLogging.logMemberJoin(member);
        });

        client.on('guildMemberRemove', (member) => {
            client.handleLogging.logMemberLeave(member);
        });

        // Role change events
        client.on('guildMemberUpdate', (oldMember, newMember) => {
            if (oldMember.partial || newMember.partial) return;
            client.handleLogging.logRoleChange(newMember, oldMember.roles.cache, newMember.roles.cache);
        });

        console.log('✅ Logging event listeners initialized');
    };

    // Initialize the event listeners
    client.handleLogging.initializeEventListeners();
};
