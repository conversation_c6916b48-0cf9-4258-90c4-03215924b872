require("dotenv").config(); // Load environment variables
const { Client, Collection, GatewayIntentBits, Events } = require("discord.js");
const fs = require("fs");

// Create the client and specify intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
  ],
});

// Initialize collections
client.commands = new Collection();
client.commandArray = [];
client.events = new Collection();

// Load functions
const functionFolders = fs.readdirSync("./src/functions");
for (const folder of functionFolders) {
  const functionFiles = fs
    .readdirSync(`./src/functions/${folder}`)
    .filter((file) => file.endsWith(".js"));
  for (const file of functionFiles) {
    console.log(`Loading function from: ./functions/${folder}/${file}`);
    const func = require(`./functions/${folder}/${file}`);
    if (typeof func === "function") {
      func(client);
    } else {
      console.error(
        `Error: ./functions/${folder}/${file} does not export a function.`
      );
    }
  }
}

// Initialize handlers
client.handleCommands();
client.handleEvents();

// Ready event
client.once(Events.ClientReady, () => {
  console.log(`✅ Logged in as ${client.user.tag}!`);
});

// Error handling
client.on('error', error => {
  console.error('Discord client error:', error);
});

process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Login
client.login(process.env.TOKEN);
