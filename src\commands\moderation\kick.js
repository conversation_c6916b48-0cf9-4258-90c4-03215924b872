const { SlashCommandBuilder, PermissionsBitField } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('kick')
    .setDescription('Kicks the member provided!')
    .addUserOption((option) =>
      option
        .setName('target')
        .setDescription('The member you\'d like to kick')
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName('reason')
        .setDescription('The reason for kicking the member provided.')
    ),

  async execute(interaction) {
    // Check if the user has the "KickMembers" permission
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.KickMembers)) {
      return interaction.reply({
        content: '❌ You do not have permission to use this command.',
        ephemeral: true,
      });
    }

    // Check if the bot has the "KickMembers" permission
    if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.KickMembers)) {
      return interaction.reply({
        content: '❌ I do not have permission to kick members.',
        ephemeral: true,
      });
    }

    const user = interaction.options.getUser('target');
    let reason = interaction.options.getString('reason') || 'No reason provided.';

    try {
      const member = await interaction.guild.members.fetch(user.id);

      // Ensure the target member is kickable
      if (!member.kickable) {
        return interaction.reply({
          content: '❌ I cannot kick this user. They may have a higher role than me.',
          ephemeral: true,
        });
      }

      // Kick the member
      await member.kick(reason);
      await interaction.reply({
        content: `✅ Successfully kicked ${user.tag}. Reason: ${reason}`,
        ephemeral: true,
      });
    } catch (error) {
      console.error(error);
      await interaction.reply({
        content: '❌ An error occurred while trying to kick the user.',
        ephemeral: true,
      });
    }
  },
};