const { SlashCommandBuilder, PermissionsBitField } = require("discord.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("ban")
    .setDescription("Bans the member provided!")
    .addUserOption((option) =>
      option
        .setName("target")
        .setDescription("The member you'd like to ban")
        .setRequired(true)
    )
    .addStringOption((option) =>
      option
        .setName("reason")
        .setDescription("The reason for banning the member provided.")
    ),

  async execute(interaction) {
    // Check if the user has the "BanMembers" permission
    if (!interaction.member.permissions.has(PermissionsBitField.Flags.BanMembers)) {
      return interaction.reply({
        content: "❌ You do not have permission to use this command.",
        ephemeral: true,
      });
    }

    // Check if the bot has the "BanMembers" permission
    if (!interaction.guild.members.me.permissions.has(PermissionsBitField.Flags.BanMembers)) {
      return interaction.reply({
        content: "❌ I do not have permission to ban members.",
        ephemeral: true,
      });
    }

    const user = interaction.options.getUser("target");
    let reason = interaction.options.getString("reason") || "No reason provided.";

    // Fetch the member to ban
    const member = interaction.guild.members.cache.get(user.id);

    if (!member) {
      return interaction.reply({
        content: "❌ The specified user is not in this server.",
        ephemeral: true,
      });
    }

    // Ensure the target member is bannable
    if (!member.bannable) {
      return interaction.reply({
        content: "❌ I cannot ban this user. They may have a higher role than me.",
        ephemeral: true,
      });
    }

    try {
      // Ban the member
      await member.ban({
        deleteMessageDays: 1, // Corrected typo
        reason: reason,
      });

      await interaction.reply({
        content: `✅ Successfully banned ${user.tag}. Reason: ${reason}`,
        ephemeral: true, // Only the user who executed the command can see this message
      });
    } catch (error) {
      console.error(error);
      await interaction.reply({
        content: "❌ An error occurred while trying to ban the user.",
        ephemeral: true,
      });
    }
  },
};